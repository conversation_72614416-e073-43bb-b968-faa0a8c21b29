2025-09-17T20:31:38.029+08:00 [INFO] Initializing MCP client 'faceRecognition' connection to https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:31:38.029+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:31:39.522+08:00 [WARN] Operation initialize failed on attempt 1/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:31:39.522+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 1s...
2025-09-17T20:31:40.523+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:31:41.710+08:00 [WARN] Operation initialize failed on attempt 2/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:31:41.710+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 2s...
2025-09-17T20:31:43.711+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:31:44.891+08:00 [WARN] Operation initialize failed on attempt 3/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:32:00.338+08:00 [INFO] Initializing MCP client 'faceRecognition' connection to https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:00.338+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:01.506+08:00 [WARN] Operation initialize failed on attempt 1/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:32:01.506+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 1s...
2025-09-17T20:32:02.507+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:03.692+08:00 [WARN] Operation initialize failed on attempt 2/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:32:03.692+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 2s...
2025-09-17T20:32:05.693+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:06.855+08:00 [WARN] Operation initialize failed on attempt 3/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:32:16.268+08:00 [INFO] Initializing MCP client 'faceRecognition' connection to https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:16.268+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:17.419+08:00 [WARN] Operation initialize failed on attempt 1/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:32:17.419+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 1s...
2025-09-17T20:32:18.421+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:19.606+08:00 [WARN] Operation initialize failed on attempt 2/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:32:19.606+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 2s...
2025-09-17T20:32:21.607+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:22.773+08:00 [WARN] Operation initialize failed on attempt 3/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:32:32.100+08:00 [INFO] Initializing MCP client 'faceRecognition' connection to https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:32.100+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:33.287+08:00 [WARN] Operation initialize failed on attempt 1/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:32:33.287+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 1s...
2025-09-17T20:32:34.288+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:35.455+08:00 [WARN] Operation initialize failed on attempt 2/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:32:35.455+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 2s...
2025-09-17T20:32:37.456+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:38.652+08:00 [WARN] Operation initialize failed on attempt 3/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:37:15.438+08:00 [INFO] Initializing MCP client 'faceRecognition' connection to https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:37:15.438+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:37:17.003+08:00 [WARN] Operation initialize failed on attempt 1/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:37:17.004+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 1s...
2025-09-17T20:37:18.005+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:37:19.200+08:00 [WARN] Operation initialize failed on attempt 2/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:37:19.200+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 2s...
2025-09-17T20:37:21.200+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:37:22.401+08:00 [WARN] Operation initialize failed on attempt 3/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
