2025-09-17T20:31:38.029+08:00 [INFO] Initializing MCP client 'faceRecognition' connection to https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:31:38.029+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:31:39.522+08:00 [WARN] Operation initialize failed on attempt 1/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:31:39.522+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 1s...
2025-09-17T20:31:40.523+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:31:41.710+08:00 [WARN] Operation initialize failed on attempt 2/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:31:41.710+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 2s...
2025-09-17T20:31:43.711+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:31:44.891+08:00 [WARN] Operation initialize failed on attempt 3/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:32:00.338+08:00 [INFO] Initializing MCP client 'faceRecognition' connection to https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:00.338+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:01.506+08:00 [WARN] Operation initialize failed on attempt 1/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:32:01.506+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 1s...
2025-09-17T20:32:02.507+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:03.692+08:00 [WARN] Operation initialize failed on attempt 2/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:32:03.692+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 2s...
2025-09-17T20:32:05.693+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:06.855+08:00 [WARN] Operation initialize failed on attempt 3/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:32:16.268+08:00 [INFO] Initializing MCP client 'faceRecognition' connection to https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:16.268+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:17.419+08:00 [WARN] Operation initialize failed on attempt 1/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:32:17.419+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 1s...
2025-09-17T20:32:18.421+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:19.606+08:00 [WARN] Operation initialize failed on attempt 2/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:32:19.606+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 2s...
2025-09-17T20:32:21.607+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:22.773+08:00 [WARN] Operation initialize failed on attempt 3/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:32:32.100+08:00 [INFO] Initializing MCP client 'faceRecognition' connection to https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:32.100+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:33.287+08:00 [WARN] Operation initialize failed on attempt 1/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:32:33.287+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 1s...
2025-09-17T20:32:34.288+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:35.455+08:00 [WARN] Operation initialize failed on attempt 2/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:32:35.455+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 2s...
2025-09-17T20:32:37.456+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:32:38.652+08:00 [WARN] Operation initialize failed on attempt 3/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:37:15.438+08:00 [INFO] Initializing MCP client 'faceRecognition' connection to https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:37:15.438+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:37:17.003+08:00 [WARN] Operation initialize failed on attempt 1/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:37:17.004+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 1s...
2025-09-17T20:37:18.005+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:37:19.200+08:00 [WARN] Operation initialize failed on attempt 2/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:37:19.200+08:00 [INFO] Retrying initialize for client 'faceRecognition' in 2s...
2025-09-17T20:37:21.200+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:37:22.401+08:00 [WARN] Operation initialize failed on attempt 3/3 for client 'faceRecognition': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:39:55.221+08:00 [INFO] Initializing MCP client 'faceRecognition' connection to https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:39:55.221+08:00 [DEBU] Creating SSE transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:39:56.585+08:00 [DEBU] Successfully connected to MCP server 'faceRecognition'
2025-09-17T20:39:56.720+08:00 [DEBU] MCP server 'faceRecognition' ping successful
2025-09-17T20:39:56.720+08:00 [INFO] MCP client 'faceRecognition' initialized successfully
2025-09-17T20:39:56.720+08:00 [DEBU] Getting tool definitions from client 'faceRecognition'
2025-09-17T20:39:56.720+08:00 [ERRO] Failed to list tools from client 'faceRecognition': connection closed: calling "tools/list": client is closing: EOF 
Stack:
1.  brainHub/internal/llms/mcp.(*UnifiedMCPClient).GetToolDefinitions
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/mcp/unified_client.go:356
2.  brainHub/internal/llms/mcp.(*UnifiedMCPManager).GetToolDefinitions
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/mcp/unified_manager.go:122
3.  brainHub/test.TestMCPSSEConnection_WithProvidedConfig.func1
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_sse_connection_test.go:65
4.  brainHub/test.TestMCPSSEConnection_WithProvidedConfig
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_sse_connection_test.go:15

2025-09-17T20:39:56.721+08:00 [INFO] Closing MCP session for client 'faceRecognition'...
2025-09-17T20:39:56.721+08:00 [INFO] MCP session for client 'faceRecognition' closed successfully
2025-09-17T20:40:09.417+08:00 [INFO] Initializing MCP client 'faceRecognition' connection to https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:40:09.418+08:00 [DEBU] Creating SSE transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:40:10.496+08:00 [DEBU] Successfully connected to MCP server 'faceRecognition'
2025-09-17T20:40:10.629+08:00 [DEBU] MCP server 'faceRecognition' ping successful
2025-09-17T20:40:10.629+08:00 [INFO] MCP client 'faceRecognition' initialized successfully
2025-09-17T20:40:10.630+08:00 [DEBU] Getting tool definitions from client 'faceRecognition'
2025-09-17T20:40:10.630+08:00 [ERRO] Failed to list tools from client 'faceRecognition': connection closed: calling "tools/list": client is closing: EOF 
Stack:
1.  brainHub/internal/llms/mcp.(*UnifiedMCPClient).GetToolDefinitions
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/mcp/unified_client.go:356
2.  brainHub/test.TestMCPSSEConnection_DirectClient.func1
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_sse_connection_test.go:129
3.  brainHub/test.TestMCPSSEConnection_DirectClient
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_sse_connection_test.go:90

2025-09-17T20:40:10.630+08:00 [INFO] Closing MCP session for client 'faceRecognition'...
2025-09-17T20:40:10.630+08:00 [INFO] MCP session for client 'faceRecognition' closed successfully
2025-09-17T20:40:22.108+08:00 [INFO] Initializing MCP client 'faceRecognition-sse' connection to https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:40:22.109+08:00 [DEBU] Creating SSE transport for client 'faceRecognition-sse' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:40:23.166+08:00 [DEBU] Successfully connected to MCP server 'faceRecognition-sse'
2025-09-17T20:40:23.297+08:00 [DEBU] MCP server 'faceRecognition-sse' ping successful
2025-09-17T20:40:23.297+08:00 [INFO] MCP client 'faceRecognition-sse' initialized successfully
2025-09-17T20:40:23.297+08:00 [INFO] Closing MCP session for client 'faceRecognition-sse'...
2025-09-17T20:40:23.297+08:00 [INFO] MCP session for client 'faceRecognition-sse' closed successfully
2025-09-17T20:40:23.297+08:00 [INFO] Initializing MCP client 'faceRecognition-streamable' connection to https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:40:23.297+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition-streamable' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:40:24.475+08:00 [WARN] Operation initialize failed on attempt 1/3 for client 'faceRecognition-streamable': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:40:24.475+08:00 [INFO] Retrying initialize for client 'faceRecognition-streamable' in 1s...
2025-09-17T20:40:25.476+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition-streamable' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:40:26.655+08:00 [WARN] Operation initialize failed on attempt 2/3 for client 'faceRecognition-streamable': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:40:26.655+08:00 [INFO] Retrying initialize for client 'faceRecognition-streamable' in 2s...
2025-09-17T20:40:28.657+08:00 [DEBU] Creating Streamable transport for client 'faceRecognition-streamable' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:40:29.858+08:00 [WARN] Operation initialize failed on attempt 3/3 for client 'faceRecognition-streamable': failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8"
2025-09-17T20:43:02.296+08:00 [INFO] Initializing MCP client 'faceRecognition' connection to https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:43:02.296+08:00 [DEBU] Creating SSE transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:43:03.631+08:00 [DEBU] Successfully connected to MCP server 'faceRecognition'
2025-09-17T20:43:03.760+08:00 [DEBU] MCP server 'faceRecognition' ping successful
2025-09-17T20:43:03.760+08:00 [INFO] MCP client 'faceRecognition' initialized successfully
2025-09-17T20:43:05.761+08:00 [DEBU] Getting tool definitions from client 'faceRecognition'
2025-09-17T20:43:05.763+08:00 [ERRO] Failed to list tools from client 'faceRecognition': connection closed: calling "tools/list": client is closing: EOF 
Stack:
1.  brainHub/internal/llms/mcp.(*UnifiedMCPClient).GetToolDefinitions
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/mcp/unified_client.go:356
2.  brainHub/test.TestMCPToolsList_Debug.func1
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_tools_list_debug_test.go:65
3.  brainHub/test.TestMCPToolsList_Debug
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_tools_list_debug_test.go:15

2025-09-17T20:43:05.763+08:00 [INFO] Closing MCP session for client 'faceRecognition'...
2025-09-17T20:43:05.763+08:00 [INFO] MCP session for client 'faceRecognition' closed successfully
2025-09-17T20:44:54.580+08:00 [INFO] Initializing MCP client 'faceRecognition' connection to https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:44:54.581+08:00 [DEBU] Creating SSE transport for client 'faceRecognition' with URL: https://csai_uat_deepface.chainsea.com.tw/mcp/
2025-09-17T20:44:56.017+08:00 [DEBU] Successfully connected to MCP server 'faceRecognition'
2025-09-17T20:44:56.145+08:00 [DEBU] MCP server 'faceRecognition' ping successful
2025-09-17T20:44:56.145+08:00 [INFO] MCP client 'faceRecognition' initialized successfully
2025-09-17T20:44:58.145+08:00 [DEBU] Getting tool definitions from client 'faceRecognition'
2025-09-17T20:44:58.145+08:00 [ERRO] Failed to list tools from client 'faceRecognition': connection closed: calling "tools/list": client is closing: EOF 
Stack:
1.  brainHub/internal/llms/mcp.(*UnifiedMCPClient).GetToolDefinitions
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/mcp/unified_client.go:360
2.  brainHub/test.TestMCPToolsList_Debug.func1
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_tools_list_debug_test.go:65
3.  brainHub/test.TestMCPToolsList_Debug
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_tools_list_debug_test.go:15

2025-09-17T20:44:58.146+08:00 [INFO] Closing MCP session for client 'faceRecognition'...
2025-09-17T20:44:58.146+08:00 [INFO] MCP session for client 'faceRecognition' closed successfully
