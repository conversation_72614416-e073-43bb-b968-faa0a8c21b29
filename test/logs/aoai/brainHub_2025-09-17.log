2025-09-17T16:06:01.910+08:00 [DEBU] Initialize with parameter: {
	"api_version": "2023-05-15",
	"base_url": "https://test.openai.azure.com/",
	"description": "",
	"embedding_model": "",
	"llm_name": "",
	"llm_type": "",
	"max_token": 4096,
	"model": "gpt-4",
	"resource_name": "",
	"temperature": 0.7,
	"token": "***masked***"
}
2025-09-17T16:06:01.911+08:00 [INFO] MCP integration initialized successfully
2025-09-17T16:06:01.911+08:00 [DEBU] Create new chat with payload: {
	"Attachments": {
		"files": [],
		"plain_text": [],
		"web_page_files": [],
		"youtube_link": []
	},
	"History": [
		"測試歷史記錄"
	],
	"SystemInstruction": "測試系統指令"
}, summary: 
2025-09-17T16:06:01.911+08:00 [DEBU] Added system instruction to chat history
2025-09-17T16:06:01.911+08:00 [INFO] === START process Attachments ===
2025-09-17T16:06:01.911+08:00 [INFO] Attachment counts: WebPages=0, Files=0, PlainText=0
2025-09-17T16:06:01.911+08:00 [DEBU] Checking token count before processing attachments...
2025-09-17T16:06:01.911+08:00 [DEBU] === START calculate Token Count ===
2025-09-17T16:06:01.911+08:00 [DEBU] LLM instance check passed
2025-09-17T16:06:01.911+08:00 [DEBU] Using cached token count: 0 (age: 0.0s)
2025-09-17T16:06:01.911+08:00 [DEBU] Token count (0) is within threshold (15000)
2025-09-17T16:06:01.911+08:00 [DEBU] Token check completed
2025-09-17T16:06:01.911+08:00 [INFO] Processing 0 Web Page files...
2025-09-17T16:06:01.911+08:00 [INFO] Processing 0 Files...
2025-09-17T16:06:01.911+08:00 [INFO] Processing 0 Plain Text items...
2025-09-17T16:06:01.911+08:00 [INFO] === COMPLETED process Attachments: processed=0, skipped=0, duration=55.417µs ===
2025-09-17T16:06:01.911+08:00 [DEBU] No dialog summary provided, processing payload history
2025-09-17T16:06:01.911+08:00 [DEBU] Processing payload history for summarization
2025-09-17T16:06:01.911+08:00 [DEBU] Found 1 history items to process
2025-09-17T16:06:01.911+08:00 [DEBU] Generating summary for 1 history items
2025-09-17T16:06:02.976+08:00 [ERRO] Failed to generate history summary: API returned unexpected status code: 401: Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).processPayloadHistory
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:292
2.  brainHub/internal/llms/aoai.(*AoAi).createNewChat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:209
3.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:151
4.  brainHub/test.TestAoAiPayloadField.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:47
5.  brainHub/test.TestAoAiPayloadField
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:15

2025-09-17T16:06:02.976+08:00 [ERRO] Failed to process payload history: Failed to generate history summary: API returned unexpected status code: 401: Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).createNewChat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:211
2.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:151
3.  brainHub/test.TestAoAiPayloadField.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:47
4.  brainHub/test.TestAoAiPayloadField
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:15

2025-09-17T16:06:02.976+08:00 [ERRO] Failed to create new chat during initialization: Failed to generate history summary: API returned unexpected status code: 401: Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:153
2.  brainHub/test.TestAoAiPayloadField.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:47
3.  brainHub/test.TestAoAiPayloadField
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:15

2025-09-17T16:06:02.976+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:02.976+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:02.976+08:00 [INFO] Received chat message: {
	"content": "測試消息內容",
	"content_type": "",
	"mime_type": ""
}
2025-09-17T16:06:02.976+08:00 [DEBU] === START calculate Token Count ===
2025-09-17T16:06:02.977+08:00 [ERRO] LLM instance is not initialized 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).calculateTokenCount
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:760
2.  brainHub/internal/llms/aoai.(*AoAi).checkAndManageTokensWithPayload
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1042
3.  brainHub/internal/llms/aoai.(*AoAi).Chat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1167
4.  brainHub/test.TestAoAiPayloadUsage.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:78
5.  brainHub/test.TestAoAiPayloadUsage
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:63

2025-09-17T16:06:02.977+08:00 [ERRO] Failed to calculate token count: LLM instance is not initialized 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).checkAndManageTokensWithPayload
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1044
2.  brainHub/internal/llms/aoai.(*AoAi).Chat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1167
3.  brainHub/test.TestAoAiPayloadUsage.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:78
4.  brainHub/test.TestAoAiPayloadUsage
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:63

2025-09-17T16:06:02.977+08:00 [WARN] Using fallback strategy: assuming token count is within threshold
2025-09-17T16:06:02.977+08:00 [ERRO] Unsupported content type:  
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Chat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1192
2.  brainHub/test.TestAoAiPayloadUsage.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:78
3.  brainHub/test.TestAoAiPayloadUsage
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:63

2025-09-17T16:06:02.977+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:02.977+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.029+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.029+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.029+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.029+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.029+08:00 [ERRO] the aoai params is nil 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:87
2.  brainHub/test.TestAoAI_InitializationWithMCP.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:36
3.  brainHub/test.TestAoAI_InitializationWithMCP
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:17

2025-09-17T16:06:03.029+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.029+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.030+08:00 [ERRO] the aoai params is nil 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:87
2.  brainHub/test.TestMCPConfigurationFlow.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:143
3.  brainHub/test.TestMCPConfigurationFlow
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:129

2025-09-17T16:06:03.030+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.030+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.030+08:00 [ERRO] the aoai params is nil 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:87
2.  brainHub/test.TestAoAi_MCPIntegration.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:36
3.  brainHub/test.TestAoAi_MCPIntegration
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:18

2025-09-17T16:06:03.030+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.030+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.057+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.057+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:06:03.058+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:06:03.058+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:10.800+08:00 [DEBU] Initialize with parameter: {
	"api_version": "2023-05-15",
	"base_url": "https://test.openai.azure.com/",
	"description": "",
	"embedding_model": "",
	"llm_name": "",
	"llm_type": "",
	"max_token": 4096,
	"model": "gpt-4",
	"resource_name": "",
	"temperature": 0.7,
	"token": "***masked***"
}
2025-09-17T16:12:10.802+08:00 [INFO] MCP integration initialized successfully
2025-09-17T16:12:10.802+08:00 [DEBU] Create new chat with payload: {
	"Attachments": {
		"files": [],
		"plain_text": [],
		"web_page_files": [],
		"youtube_link": []
	},
	"History": [
		"測試歷史記錄"
	],
	"SystemInstruction": "測試系統指令"
}, summary: 
2025-09-17T16:12:10.802+08:00 [DEBU] Added system instruction to chat history
2025-09-17T16:12:10.802+08:00 [INFO] === START process Attachments ===
2025-09-17T16:12:10.802+08:00 [INFO] Attachment counts: WebPages=0, Files=0, PlainText=0
2025-09-17T16:12:10.802+08:00 [DEBU] Checking token count before processing attachments...
2025-09-17T16:12:10.802+08:00 [DEBU] === START calculate Token Count ===
2025-09-17T16:12:10.802+08:00 [DEBU] LLM instance check passed
2025-09-17T16:12:10.802+08:00 [DEBU] Using cached token count: 0 (age: 0.0s)
2025-09-17T16:12:10.802+08:00 [DEBU] Token count (0) is within threshold (15000)
2025-09-17T16:12:10.802+08:00 [DEBU] Token check completed
2025-09-17T16:12:10.802+08:00 [INFO] Processing 0 Web Page files...
2025-09-17T16:12:10.802+08:00 [INFO] Processing 0 Files...
2025-09-17T16:12:10.802+08:00 [INFO] Processing 0 Plain Text items...
2025-09-17T16:12:10.802+08:00 [INFO] === COMPLETED process Attachments: processed=0, skipped=0, duration=105.75µs ===
2025-09-17T16:12:10.802+08:00 [DEBU] No dialog summary provided, processing payload history
2025-09-17T16:12:10.802+08:00 [DEBU] Processing payload history for summarization
2025-09-17T16:12:10.802+08:00 [DEBU] Found 1 history items to process
2025-09-17T16:12:10.802+08:00 [DEBU] Generating summary for 1 history items
2025-09-17T16:12:11.839+08:00 [ERRO] Failed to generate history summary: API returned unexpected status code: 401: Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).processPayloadHistory
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:292
2.  brainHub/internal/llms/aoai.(*AoAi).createNewChat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:209
3.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:151
4.  brainHub/test.TestAoAiPayloadField.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:47
5.  brainHub/test.TestAoAiPayloadField
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:15

2025-09-17T16:12:11.840+08:00 [ERRO] Failed to process payload history: Failed to generate history summary: API returned unexpected status code: 401: Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).createNewChat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:211
2.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:151
3.  brainHub/test.TestAoAiPayloadField.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:47
4.  brainHub/test.TestAoAiPayloadField
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:15

2025-09-17T16:12:11.840+08:00 [ERRO] Failed to create new chat during initialization: Failed to generate history summary: API returned unexpected status code: 401: Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:153
2.  brainHub/test.TestAoAiPayloadField.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:47
3.  brainHub/test.TestAoAiPayloadField
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:15

2025-09-17T16:12:11.841+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.841+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.841+08:00 [INFO] Received chat message: {
	"content": "測試消息內容",
	"content_type": "",
	"mime_type": ""
}
2025-09-17T16:12:11.841+08:00 [DEBU] === START calculate Token Count ===
2025-09-17T16:12:11.841+08:00 [ERRO] LLM instance is not initialized 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).calculateTokenCount
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:760
2.  brainHub/internal/llms/aoai.(*AoAi).checkAndManageTokensWithPayload
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1042
3.  brainHub/internal/llms/aoai.(*AoAi).Chat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1167
4.  brainHub/test.TestAoAiPayloadUsage.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:78
5.  brainHub/test.TestAoAiPayloadUsage
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:63

2025-09-17T16:12:11.841+08:00 [ERRO] Failed to calculate token count: LLM instance is not initialized 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).checkAndManageTokensWithPayload
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1044
2.  brainHub/internal/llms/aoai.(*AoAi).Chat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1167
3.  brainHub/test.TestAoAiPayloadUsage.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:78
4.  brainHub/test.TestAoAiPayloadUsage
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:63

2025-09-17T16:12:11.841+08:00 [WARN] Using fallback strategy: assuming token count is within threshold
2025-09-17T16:12:11.841+08:00 [ERRO] Unsupported content type:  
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Chat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1192
2.  brainHub/test.TestAoAiPayloadUsage.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:78
3.  brainHub/test.TestAoAiPayloadUsage
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:63

2025-09-17T16:12:11.842+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.842+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.895+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.895+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.897+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.897+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.897+08:00 [ERRO] the aoai params is nil 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:87
2.  brainHub/test.TestAoAI_InitializationWithMCP.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:36
3.  brainHub/test.TestAoAI_InitializationWithMCP
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:17

2025-09-17T16:12:11.897+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.898+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.899+08:00 [ERRO] the aoai params is nil 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:87
2.  brainHub/test.TestMCPConfigurationFlow.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:143
3.  brainHub/test.TestMCPConfigurationFlow
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:129

2025-09-17T16:12:11.899+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.899+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.900+08:00 [ERRO] the aoai params is nil 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:87
2.  brainHub/test.TestAoAi_MCPIntegration.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:36
3.  brainHub/test.TestAoAi_MCPIntegration
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:18

2025-09-17T16:12:11.900+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.900+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.932+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.932+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.932+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.932+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.932+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.932+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.932+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.932+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.932+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.932+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.932+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.932+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.932+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.932+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.932+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.932+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.932+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.932+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.932+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.932+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.932+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.932+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.932+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.932+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.932+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.932+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.932+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.932+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.932+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.932+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.932+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.932+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.932+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.932+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.932+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:12:11.933+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:12:11.933+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:51.536+08:00 [DEBU] Initialize with parameter: {
	"api_version": "2023-05-15",
	"base_url": "https://test.openai.azure.com/",
	"description": "",
	"embedding_model": "",
	"llm_name": "",
	"llm_type": "",
	"max_token": 4096,
	"model": "gpt-4",
	"resource_name": "",
	"temperature": 0.7,
	"token": "***masked***"
}
2025-09-17T16:14:51.537+08:00 [INFO] MCP integration initialized successfully
2025-09-17T16:14:51.537+08:00 [DEBU] Create new chat with payload: {
	"Attachments": {
		"files": [],
		"plain_text": [],
		"web_page_files": [],
		"youtube_link": []
	},
	"History": [
		"測試歷史記錄"
	],
	"SystemInstruction": "測試系統指令"
}, summary: 
2025-09-17T16:14:51.537+08:00 [DEBU] Added system instruction to chat history
2025-09-17T16:14:51.537+08:00 [INFO] === START process Attachments ===
2025-09-17T16:14:51.537+08:00 [INFO] Attachment counts: WebPages=0, Files=0, PlainText=0
2025-09-17T16:14:51.537+08:00 [DEBU] Checking token count before processing attachments...
2025-09-17T16:14:51.537+08:00 [DEBU] === START calculate Token Count ===
2025-09-17T16:14:51.538+08:00 [DEBU] LLM instance check passed
2025-09-17T16:14:51.538+08:00 [DEBU] Using cached token count: 0 (age: 0.0s)
2025-09-17T16:14:51.538+08:00 [DEBU] Token count (0) is within threshold (15000)
2025-09-17T16:14:51.538+08:00 [DEBU] Token check completed
2025-09-17T16:14:51.538+08:00 [INFO] Processing 0 Web Page files...
2025-09-17T16:14:51.538+08:00 [INFO] Processing 0 Files...
2025-09-17T16:14:51.538+08:00 [INFO] Processing 0 Plain Text items...
2025-09-17T16:14:51.538+08:00 [INFO] === COMPLETED process Attachments: processed=0, skipped=0, duration=93µs ===
2025-09-17T16:14:51.538+08:00 [DEBU] No dialog summary provided, processing payload history
2025-09-17T16:14:51.538+08:00 [DEBU] Processing payload history for summarization
2025-09-17T16:14:51.538+08:00 [DEBU] Found 1 history items to process
2025-09-17T16:14:51.538+08:00 [DEBU] Generating summary for 1 history items
2025-09-17T16:14:53.025+08:00 [ERRO] Failed to generate history summary: API returned unexpected status code: 401: Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).processPayloadHistory
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:292
2.  brainHub/internal/llms/aoai.(*AoAi).createNewChat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:209
3.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:151
4.  brainHub/test.TestAoAiPayloadField.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:47
5.  brainHub/test.TestAoAiPayloadField
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:15

2025-09-17T16:14:53.025+08:00 [ERRO] Failed to process payload history: Failed to generate history summary: API returned unexpected status code: 401: Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).createNewChat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:211
2.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:151
3.  brainHub/test.TestAoAiPayloadField.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:47
4.  brainHub/test.TestAoAiPayloadField
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:15

2025-09-17T16:14:53.025+08:00 [ERRO] Failed to create new chat during initialization: Failed to generate history summary: API returned unexpected status code: 401: Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:153
2.  brainHub/test.TestAoAiPayloadField.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:47
3.  brainHub/test.TestAoAiPayloadField
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:15

2025-09-17T16:14:53.025+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.025+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.025+08:00 [INFO] Received chat message: {
	"content": "測試消息內容",
	"content_type": "",
	"mime_type": ""
}
2025-09-17T16:14:53.025+08:00 [DEBU] === START calculate Token Count ===
2025-09-17T16:14:53.026+08:00 [ERRO] LLM instance is not initialized 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).calculateTokenCount
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:760
2.  brainHub/internal/llms/aoai.(*AoAi).checkAndManageTokensWithPayload
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1042
3.  brainHub/internal/llms/aoai.(*AoAi).Chat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1167
4.  brainHub/test.TestAoAiPayloadUsage.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:78
5.  brainHub/test.TestAoAiPayloadUsage
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:63

2025-09-17T16:14:53.026+08:00 [ERRO] Failed to calculate token count: LLM instance is not initialized 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).checkAndManageTokensWithPayload
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1044
2.  brainHub/internal/llms/aoai.(*AoAi).Chat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1167
3.  brainHub/test.TestAoAiPayloadUsage.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:78
4.  brainHub/test.TestAoAiPayloadUsage
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:63

2025-09-17T16:14:53.026+08:00 [WARN] Using fallback strategy: assuming token count is within threshold
2025-09-17T16:14:53.026+08:00 [ERRO] Unsupported content type:  
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Chat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1192
2.  brainHub/test.TestAoAiPayloadUsage.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:78
3.  brainHub/test.TestAoAiPayloadUsage
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:63

2025-09-17T16:14:53.026+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.026+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.079+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.079+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.080+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.080+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.080+08:00 [ERRO] the aoai params is nil 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:87
2.  brainHub/test.TestAoAI_InitializationWithMCP.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:36
3.  brainHub/test.TestAoAI_InitializationWithMCP
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:17

2025-09-17T16:14:53.080+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.080+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.082+08:00 [ERRO] the aoai params is nil 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:87
2.  brainHub/test.TestMCPConfigurationFlow.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:143
3.  brainHub/test.TestMCPConfigurationFlow
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:129

2025-09-17T16:14:53.082+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.082+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.082+08:00 [ERRO] the aoai params is nil 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:87
2.  brainHub/test.TestAoAi_MCPIntegration.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:36
3.  brainHub/test.TestAoAi_MCPIntegration
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:18

2025-09-17T16:14:53.082+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.082+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.113+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.113+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.114+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.114+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.114+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.114+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.114+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.114+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.114+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.114+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.114+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.114+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.114+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.114+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.114+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.114+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.114+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.114+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.114+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.114+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.114+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T16:14:53.114+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T16:14:53.114+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:02.561+08:00 [DEBU] Initialize with parameter: {
	"api_version": "2023-05-15",
	"base_url": "https://test.openai.azure.com/",
	"description": "",
	"embedding_model": "",
	"llm_name": "",
	"llm_type": "",
	"max_token": 4096,
	"model": "gpt-4",
	"resource_name": "",
	"temperature": 0.7,
	"token": "***masked***"
}
2025-09-17T17:05:02.562+08:00 [INFO] MCP integration initialized successfully
2025-09-17T17:05:02.562+08:00 [DEBU] Create new chat with payload: {
	"Attachments": {
		"files": [],
		"plain_text": [],
		"web_page_files": [],
		"youtube_link": []
	},
	"History": [
		"測試歷史記錄"
	],
	"SystemInstruction": "測試系統指令"
}, summary: 
2025-09-17T17:05:02.562+08:00 [DEBU] Added system instruction to chat history
2025-09-17T17:05:02.562+08:00 [INFO] === START process Attachments ===
2025-09-17T17:05:02.562+08:00 [INFO] Attachment counts: WebPages=0, Files=0, PlainText=0
2025-09-17T17:05:02.562+08:00 [DEBU] Checking token count before processing attachments...
2025-09-17T17:05:02.562+08:00 [DEBU] === START calculate Token Count ===
2025-09-17T17:05:02.563+08:00 [DEBU] LLM instance check passed
2025-09-17T17:05:02.563+08:00 [DEBU] Using cached token count: 0 (age: 0.0s)
2025-09-17T17:05:02.563+08:00 [DEBU] Token count (0) is within threshold (15000)
2025-09-17T17:05:02.563+08:00 [DEBU] Token check completed
2025-09-17T17:05:02.563+08:00 [INFO] Processing 0 Web Page files...
2025-09-17T17:05:02.563+08:00 [INFO] Processing 0 Files...
2025-09-17T17:05:02.563+08:00 [INFO] Processing 0 Plain Text items...
2025-09-17T17:05:02.563+08:00 [INFO] === COMPLETED process Attachments: processed=0, skipped=0, duration=38.791µs ===
2025-09-17T17:05:02.563+08:00 [DEBU] No dialog summary provided, processing payload history
2025-09-17T17:05:02.563+08:00 [DEBU] Processing payload history for summarization
2025-09-17T17:05:02.563+08:00 [DEBU] Found 1 history items to process
2025-09-17T17:05:02.563+08:00 [DEBU] Generating summary for 1 history items
2025-09-17T17:05:03.609+08:00 [ERRO] Failed to generate history summary: API returned unexpected status code: 401: Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).processPayloadHistory
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:292
2.  brainHub/internal/llms/aoai.(*AoAi).createNewChat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:209
3.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:151
4.  brainHub/test.TestAoAiPayloadField.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:47
5.  brainHub/test.TestAoAiPayloadField
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:15

2025-09-17T17:05:03.609+08:00 [ERRO] Failed to process payload history: Failed to generate history summary: API returned unexpected status code: 401: Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).createNewChat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:211
2.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:151
3.  brainHub/test.TestAoAiPayloadField.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:47
4.  brainHub/test.TestAoAiPayloadField
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:15

2025-09-17T17:05:03.609+08:00 [ERRO] Failed to create new chat during initialization: Failed to generate history summary: API returned unexpected status code: 401: Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:153
2.  brainHub/test.TestAoAiPayloadField.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:47
3.  brainHub/test.TestAoAiPayloadField
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:15

2025-09-17T17:05:03.609+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.609+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.609+08:00 [INFO] Received chat message: {
	"content": "測試消息內容",
	"content_type": "",
	"mime_type": ""
}
2025-09-17T17:05:03.609+08:00 [DEBU] === START calculate Token Count ===
2025-09-17T17:05:03.609+08:00 [ERRO] LLM instance is not initialized 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).calculateTokenCount
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:760
2.  brainHub/internal/llms/aoai.(*AoAi).checkAndManageTokensWithPayload
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1042
3.  brainHub/internal/llms/aoai.(*AoAi).Chat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1167
4.  brainHub/test.TestAoAiPayloadUsage.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:78
5.  brainHub/test.TestAoAiPayloadUsage
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:63

2025-09-17T17:05:03.609+08:00 [ERRO] Failed to calculate token count: LLM instance is not initialized 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).checkAndManageTokensWithPayload
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1044
2.  brainHub/internal/llms/aoai.(*AoAi).Chat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1167
3.  brainHub/test.TestAoAiPayloadUsage.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:78
4.  brainHub/test.TestAoAiPayloadUsage
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:63

2025-09-17T17:05:03.609+08:00 [WARN] Using fallback strategy: assuming token count is within threshold
2025-09-17T17:05:03.609+08:00 [ERRO] Unsupported content type:  
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Chat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1192
2.  brainHub/test.TestAoAiPayloadUsage.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:78
3.  brainHub/test.TestAoAiPayloadUsage
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:63

2025-09-17T17:05:03.609+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.609+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.662+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.662+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.663+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.663+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.663+08:00 [ERRO] the aoai params is nil 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:87
2.  brainHub/test.TestAoAI_InitializationWithMCP.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:36
3.  brainHub/test.TestAoAI_InitializationWithMCP
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:17

2025-09-17T17:05:03.663+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.663+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.664+08:00 [ERRO] the aoai params is nil 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:87
2.  brainHub/test.TestMCPConfigurationFlow.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:143
3.  brainHub/test.TestMCPConfigurationFlow
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:129

2025-09-17T17:05:03.664+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.664+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.664+08:00 [ERRO] the aoai params is nil 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:87
2.  brainHub/test.TestAoAi_MCPIntegration.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:36
3.  brainHub/test.TestAoAi_MCPIntegration
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:18

2025-09-17T17:05:03.664+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.664+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.693+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.693+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.694+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.694+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.695+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.695+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.695+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.695+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.695+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.695+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.695+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.695+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.695+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.695+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.695+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.695+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.695+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.695+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.695+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.695+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.695+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.695+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.695+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.695+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.695+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.695+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.695+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.695+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.695+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.695+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.695+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.695+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.695+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.695+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.695+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T17:05:03.695+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T17:05:03.695+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:17.398+08:00 [DEBU] Initialize with parameter: {
	"api_version": "2023-05-15",
	"base_url": "https://test.openai.azure.com/",
	"description": "",
	"embedding_model": "",
	"llm_name": "",
	"llm_type": "",
	"max_token": 4096,
	"model": "gpt-4",
	"resource_name": "",
	"temperature": 0.7,
	"token": "***masked***"
}
2025-09-17T18:28:17.399+08:00 [INFO] MCP integration initialized successfully
2025-09-17T18:28:17.399+08:00 [DEBU] Create new chat with payload: {
	"Attachments": {
		"files": [],
		"plain_text": [],
		"web_page_files": [],
		"youtube_link": []
	},
	"History": [
		"測試歷史記錄"
	],
	"SystemInstruction": "測試系統指令"
}, summary: 
2025-09-17T18:28:17.399+08:00 [DEBU] Added system instruction to chat history
2025-09-17T18:28:17.399+08:00 [INFO] === START process Attachments ===
2025-09-17T18:28:17.399+08:00 [INFO] Attachment counts: WebPages=0, Files=0, PlainText=0
2025-09-17T18:28:17.399+08:00 [DEBU] Checking token count before processing attachments...
2025-09-17T18:28:17.399+08:00 [DEBU] === START calculate Token Count ===
2025-09-17T18:28:17.399+08:00 [DEBU] LLM instance check passed
2025-09-17T18:28:17.399+08:00 [DEBU] Using cached token count: 0 (age: 0.0s)
2025-09-17T18:28:17.399+08:00 [DEBU] Token count (0) is within threshold (15000)
2025-09-17T18:28:17.399+08:00 [DEBU] Token check completed
2025-09-17T18:28:17.399+08:00 [INFO] Processing 0 Web Page files...
2025-09-17T18:28:17.399+08:00 [INFO] Processing 0 Files...
2025-09-17T18:28:17.399+08:00 [INFO] Processing 0 Plain Text items...
2025-09-17T18:28:17.399+08:00 [INFO] === COMPLETED process Attachments: processed=0, skipped=0, duration=45.834µs ===
2025-09-17T18:28:17.399+08:00 [DEBU] No dialog summary provided, processing payload history
2025-09-17T18:28:17.399+08:00 [DEBU] Processing payload history for summarization
2025-09-17T18:28:17.399+08:00 [DEBU] Found 1 history items to process
2025-09-17T18:28:17.399+08:00 [DEBU] Generating summary for 1 history items
2025-09-17T18:28:18.770+08:00 [ERRO] Failed to generate history summary: API returned unexpected status code: 401: Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).processPayloadHistory
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:292
2.  brainHub/internal/llms/aoai.(*AoAi).createNewChat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:209
3.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:151
4.  brainHub/test.TestAoAiPayloadField.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:47
5.  brainHub/test.TestAoAiPayloadField
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:15

2025-09-17T18:28:18.771+08:00 [ERRO] Failed to process payload history: Failed to generate history summary: API returned unexpected status code: 401: Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).createNewChat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:211
2.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:151
3.  brainHub/test.TestAoAiPayloadField.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:47
4.  brainHub/test.TestAoAiPayloadField
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:15

2025-09-17T18:28:18.771+08:00 [ERRO] Failed to create new chat during initialization: Failed to generate history summary: API returned unexpected status code: 401: Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource. 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:153
2.  brainHub/test.TestAoAiPayloadField.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:47
3.  brainHub/test.TestAoAiPayloadField
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:15

2025-09-17T18:28:18.771+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.771+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.771+08:00 [INFO] Received chat message: {
	"content": "測試消息內容",
	"content_type": "",
	"mime_type": ""
}
2025-09-17T18:28:18.771+08:00 [DEBU] === START calculate Token Count ===
2025-09-17T18:28:18.771+08:00 [ERRO] LLM instance is not initialized 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).calculateTokenCount
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:760
2.  brainHub/internal/llms/aoai.(*AoAi).checkAndManageTokensWithPayload
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1042
3.  brainHub/internal/llms/aoai.(*AoAi).Chat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1167
4.  brainHub/test.TestAoAiPayloadUsage.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:78
5.  brainHub/test.TestAoAiPayloadUsage
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:63

2025-09-17T18:28:18.771+08:00 [ERRO] Failed to calculate token count: LLM instance is not initialized 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).checkAndManageTokensWithPayload
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1044
2.  brainHub/internal/llms/aoai.(*AoAi).Chat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1167
3.  brainHub/test.TestAoAiPayloadUsage.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:78
4.  brainHub/test.TestAoAiPayloadUsage
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:63

2025-09-17T18:28:18.771+08:00 [WARN] Using fallback strategy: assuming token count is within threshold
2025-09-17T18:28:18.771+08:00 [ERRO] Unsupported content type:  
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Chat
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:1192
2.  brainHub/test.TestAoAiPayloadUsage.func1
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:78
3.  brainHub/test.TestAoAiPayloadUsage
    /Users/<USER>/Source/Ai app/brainHub/test/aoai_payload_test.go:63

2025-09-17T18:28:18.771+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.771+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.771+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.771+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.772+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.773+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.773+08:00 [ERRO] the aoai params is nil 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:87
2.  brainHub/test.TestAoAI_InitializationWithMCP.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:36
3.  brainHub/test.TestAoAI_InitializationWithMCP
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:17

2025-09-17T18:28:18.773+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.773+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.773+08:00 [ERRO] the aoai params is nil 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:87
2.  brainHub/test.TestMCPConfigurationFlow.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:143
3.  brainHub/test.TestMCPConfigurationFlow
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:129

2025-09-17T18:28:18.773+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.773+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.773+08:00 [ERRO] the aoai params is nil 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:87
2.  brainHub/test.TestAoAi_MCPIntegration.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:36
3.  brainHub/test.TestAoAi_MCPIntegration
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:18

2025-09-17T18:28:18.773+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.774+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.798+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.798+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T18:28:18.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T18:28:18.799+08:00 [INFO] Azure OpenAI service resources released successfully
2025-09-17T20:32:48.799+08:00 [ERRO] the aoai params is nil 
Stack:
1.  brainHub/internal/llms/aoai.(*AoAi).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/aoai/aoai.go:87
2.  brainHub/test.TestAoAi_MCPIntegration.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:36
3.  brainHub/test.TestAoAi_MCPIntegration
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:18

2025-09-17T20:32:48.799+08:00 [DEBU] Releasing Azure OpenAI service resources
2025-09-17T20:32:48.799+08:00 [INFO] Azure OpenAI service resources released successfully
