package test

import (
	"context"
	"testing"
	"time"

	"brainHub/internal/llms/mcp"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestMCPLocalServer_Connection 測試連接到本地 MCP 服務器
func TestMCPLocalServer_Connection(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 測試連接到本地 MCP 服務器（如果運行的話）
		clientConfig := &mcp.MCPClientConfig{
			Name:      "faceRecognition-local",
			Type:      "streamable", // 本地服務器使用 streamable
			ServerURL: "http://localhost:8080/",
			Headers:   map[string]string{}, // 本地服務器可能不需要 API Key
			Timeout:   60 * time.Second,
			MaxRetries: 3,
			AutoApprove: []string{},
			Disabled:    false,
		}

		t.Logf("=== Testing Local MCP Server Connection ===")
		t.Logf("Server URL: %s", clientConfig.ServerURL)
		t.Logf("Type: %s", clientConfig.Type)

		// 創建統一 MCP 客戶端
		client := mcp.NewUnifiedMCPClient(clientConfig)
		t.AssertNE(client, nil)

		// 測試初始化
		t.Logf("Step 1: Initializing local MCP client...")
		err := client.Initialize(ctx)
		if err != nil {
			t.Logf("❌ Local MCP server not running or failed to connect: %v", err)
			t.Logf("💡 To start the local MCP server, run:")
			t.Logf("   export FACE_RECOGNITION_API_KEY=\"NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU\"")
			t.Logf("   ./scripts/start_face_recognition_mcp.sh")
			return
		}
		t.Logf("✅ Local MCP client initialization succeeded")

		// 檢查連接狀態
		connected := client.IsConnected()
		t.Logf("Connected: %v", connected)

		if connected {
			// 嘗試獲取工具定義
			t.Logf("Step 2: Attempting to get tool definitions from local server...")
			tools, err := client.GetToolDefinitions(ctx)
			if err != nil {
				t.Logf("❌ GetToolDefinitions failed: %v", err)
			} else {
				t.Logf("✅ GetToolDefinitions succeeded: %d tools", len(tools))
				for i, tool := range tools {
					t.Logf("Tool %d: %s - %s", i+1, tool.Name, tool.Description)
				}
			}
		}

		// 清理
		client.Close()
		t.Logf("Local MCP client closed")
	})
}

// TestMCPLocalServer_WithAPIKey 測試本地服務器是否需要 API Key
func TestMCPLocalServer_WithAPIKey(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 測試帶 API Key 的本地連接
		clientConfig := &mcp.MCPClientConfig{
			Name:      "faceRecognition-local-with-key",
			Type:      "streamable",
			ServerURL: "http://localhost:8080/",
			Headers: map[string]string{
				"CS-API-Key": "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU",
			},
			Timeout:     60 * time.Second,
			MaxRetries:  3,
			AutoApprove: []string{},
			Disabled:    false,
		}

		t.Logf("=== Testing Local MCP Server with API Key ===")

		client := mcp.NewUnifiedMCPClient(clientConfig)
		t.AssertNE(client, nil)

		err := client.Initialize(ctx)
		if err != nil {
			t.Logf("❌ Local MCP server with API key failed: %v", err)
			return
		}
		t.Logf("✅ Local MCP client with API key succeeded")

		connected := client.IsConnected()
		t.Logf("Connected: %v", connected)

		if connected {
			tools, err := client.GetToolDefinitions(ctx)
			if err != nil {
				t.Logf("❌ GetToolDefinitions failed: %v", err)
			} else {
				t.Logf("✅ GetToolDefinitions succeeded: %d tools", len(tools))
				for i, tool := range tools {
					t.Logf("Tool %d: %s - %s", i+1, tool.Name, tool.Description)
				}
			}
		}

		client.Close()
		t.Logf("Local MCP client with API key closed")
	})
}

// TestMCPLocalServer_CompareWithRemote 比較本地和遠程服務器
func TestMCPLocalServer_CompareWithRemote(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		t.Logf("=== Comparing Local vs Remote MCP Servers ===")

		// 本地服務器配置
		localConfig := &mcp.MCPClientConfig{
			Name:        "local",
			Type:        "streamable",
			ServerURL:   "http://localhost:8080/",
			Headers:     map[string]string{},
			Timeout:     30 * time.Second,
			MaxRetries:  1,
			AutoApprove: []string{},
			Disabled:    false,
		}

		// 遠程服務器配置
		remoteConfig := &mcp.MCPClientConfig{
			Name:      "remote",
			Type:      "sse",
			ServerURL: "https://csai_uat_deepface.chainsea.com.tw/mcp/",
			Headers: map[string]string{
				"CS-API-Key": "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU",
			},
			Timeout:     30 * time.Second,
			MaxRetries:  1,
			AutoApprove: []string{},
			Disabled:    false,
		}

		// 測試本地服務器
		t.Logf("--- Testing Local Server ---")
		localClient := mcp.NewUnifiedMCPClient(localConfig)
		localErr := localClient.Initialize(ctx)
		if localErr != nil {
			t.Logf("Local server failed: %v", localErr)
		} else {
			t.Logf("Local server succeeded!")
			if localClient.IsConnected() {
				tools, err := localClient.GetToolDefinitions(ctx)
				if err != nil {
					t.Logf("Local tools/list failed: %v", err)
				} else {
					t.Logf("Local tools/list succeeded: %d tools", len(tools))
				}
			}
		}
		localClient.Close()

		// 測試遠程服務器
		t.Logf("--- Testing Remote Server ---")
		remoteClient := mcp.NewUnifiedMCPClient(remoteConfig)
		remoteErr := remoteClient.Initialize(ctx)
		if remoteErr != nil {
			t.Logf("Remote server failed: %v", remoteErr)
		} else {
			t.Logf("Remote server succeeded!")
			if remoteClient.IsConnected() {
				tools, err := remoteClient.GetToolDefinitions(ctx)
				if err != nil {
					t.Logf("Remote tools/list failed: %v", err)
				} else {
					t.Logf("Remote tools/list succeeded: %d tools", len(tools))
				}
			}
		}
		remoteClient.Close()

		// 總結
		t.Logf("--- Summary ---")
		if localErr == nil && remoteErr != nil {
			t.Logf("✅ Local server works, remote server fails")
			t.Logf("💡 This suggests mcpClient connects to local wrapper server")
		} else if localErr != nil && remoteErr == nil {
			t.Logf("❌ Local server fails, remote server works")
			t.Logf("💡 This suggests mcpClient connects directly to remote server")
		} else if localErr == nil && remoteErr == nil {
			t.Logf("✅ Both servers work")
			t.Logf("💡 Need to check which one mcpClient actually uses")
		} else {
			t.Logf("❌ Both servers fail")
			t.Logf("💡 There might be other configuration issues")
		}
	})
}

// TestMCPLocalServer_ToolCall 測試本地服務器的工具調用
func TestMCPLocalServer_ToolCall(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		clientConfig := &mcp.MCPClientConfig{
			Name:        "faceRecognition-local-toolcall",
			Type:        "streamable",
			ServerURL:   "http://localhost:8080/",
			Headers:     map[string]string{},
			Timeout:     60 * time.Second,
			MaxRetries:  3,
			AutoApprove: []string{},
			Disabled:    false,
		}

		t.Logf("=== Testing Local MCP Server Tool Call ===")

		client := mcp.NewUnifiedMCPClient(clientConfig)
		t.AssertNE(client, nil)

		err := client.Initialize(ctx)
		if err != nil {
			t.Logf("❌ Local MCP server not available: %v", err)
			return
		}
		t.Logf("✅ Local MCP client initialized")

		if !client.IsConnected() {
			t.Logf("❌ Client not connected")
			return
		}

		// 獲取工具列表
		tools, err := client.GetToolDefinitions(ctx)
		if err != nil {
			t.Logf("❌ Failed to get tools: %v", err)
			client.Close()
			return
		}

		t.Logf("✅ Got %d tools from local server", len(tools))
		for i, tool := range tools {
			t.Logf("Tool %d: %s", i+1, tool.Name)
		}

		// 如果有工具，嘗試調用第一個
		if len(tools) > 0 {
			firstTool := tools[0]
			t.Logf("Attempting to call tool: %s", firstTool.Name)

			// 這裡需要根據實際工具的參數來構造調用
			// 暫時使用空參數測試
			result, err := client.CallTool(ctx, firstTool.Name, map[string]interface{}{})
			if err != nil {
				t.Logf("❌ Tool call failed: %v", err)
			} else {
				t.Logf("✅ Tool call succeeded: %+v", result)
			}
		}

		client.Close()
		t.Logf("Local MCP client closed")
	})
}
