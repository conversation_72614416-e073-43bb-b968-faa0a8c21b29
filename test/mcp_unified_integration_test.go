package test

import (
	"context"
	"testing"
	"time"

	"brainHub/internal/llms/mcp"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestUnifiedMCPIntegration_SSEConnection 測試統一 MCP 集成的 SSE 連接功能
func TestUnifiedMCPIntegration_SSEConnection(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建統一 MCP 集成實例
		integration, err := mcp.NewUnifiedMCPIntegration(ctx)
		if err != nil {
			t.Logf("Failed to create unified MCP integration (expected if no config): %v", err)
			return
		}

		if integration == nil {
			t.Logf("MCP is disabled or not configured")
			return
		}

		// 測試連接狀態
		connectionStatus := integration.GetConnectionStatus()
		t.Logf("Connection status: %+v", connectionStatus)

		// 測試健康檢查
		healthCheck := integration.HealthCheck(ctx)
		t.Logf("Health check result: %+v", healthCheck)

		// 測試客戶端統計
		stats := integration.GetClientStats(ctx)
		t.Logf("Client stats: %+v", stats)

		// 清理
		err = integration.Close(ctx)
		if err != nil {
			t.Logf("Failed to close integration: %v", err)
		}
	})
}

// TestUnifiedMCPIntegration_ToolListRetrieval 測試工具列表獲取功能
func TestUnifiedMCPIntegration_ToolListRetrieval(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建統一 MCP 集成實例
		integration, err := mcp.NewUnifiedMCPIntegration(ctx)
		if err != nil {
			t.Logf("Failed to create unified MCP integration (expected if no config): %v", err)
			return
		}

		if integration == nil {
			t.Logf("MCP is disabled or not configured")
			return
		}

		// 測試工具定義獲取（這是修復的核心功能）
		tools, err := integration.GetToolDefinitions(ctx)
		if err != nil {
			t.Logf("Failed to get tool definitions: %v", err)
		} else {
			t.Logf("Successfully retrieved %d tool definitions", len(tools))
			for i, tool := range tools {
				t.Logf("Tool %d: %s - %s", i+1, tool.Name, tool.Description)
			}
		}

		// 測試工具數量獲取
		toolCount, err := integration.GetToolCount(ctx)
		if err != nil {
			t.Logf("Failed to get tool count: %v", err)
		} else {
			t.Logf("Total tool count: %d", toolCount)
		}

		// 清理
		err = integration.Close(ctx)
		if err != nil {
			t.Logf("Failed to close integration: %v", err)
		}
	})
}

// TestUnifiedMCPIntegration_ConnectionStability 測試連接穩定性
func TestUnifiedMCPIntegration_ConnectionStability(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建統一 MCP 集成實例
		integration, err := mcp.NewUnifiedMCPIntegration(ctx)
		if err != nil {
			t.Logf("Failed to create unified MCP integration (expected if no config): %v", err)
			return
		}

		if integration == nil {
			t.Logf("MCP is disabled or not configured")
			return
		}

		// 測試多次連接狀態檢查
		for i := 0; i < 3; i++ {
			t.Logf("Connection stability test iteration %d", i+1)

			connectionStatus := integration.GetConnectionStatus()
			t.Logf("Iteration %d - Connection status: %+v", i+1, connectionStatus)

			// 測試連接測試功能
			clients := integration.ListClients()
			for _, client := range clients {
				clientName := client["name"].(string)
				err := integration.TestConnection(ctx, clientName)
				if err != nil {
					t.Logf("Connection test failed for client '%s': %v", clientName, err)
				} else {
					t.Logf("Connection test passed for client '%s'", clientName)
				}
			}

			// 短暫等待
			time.Sleep(100 * time.Millisecond)
		}

		// 清理
		err = integration.Close(ctx)
		if err != nil {
			t.Logf("Failed to close integration: %v", err)
		}
	})
}

// TestUnifiedMCPIntegration_BackwardCompatibility 測試向後相容性
func TestUnifiedMCPIntegration_BackwardCompatibility(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建統一 MCP 集成實例
		integration, err := mcp.NewUnifiedMCPIntegration(ctx)
		if err != nil {
			t.Logf("Failed to create unified MCP integration (expected if no config): %v", err)
			return
		}

		if integration == nil {
			t.Logf("MCP is disabled or not configured")
			return
		}

		// 測試所有舊接口是否仍然可用
		t.Logf("Testing backward compatibility...")

		// 1. GetToolDefinitions 接口
		tools, err := integration.GetToolDefinitions(ctx)
		if err != nil {
			t.Logf("GetToolDefinitions failed: %v", err)
		} else {
			t.Logf("GetToolDefinitions succeeded: %d tools", len(tools))
		}

		// 2. IsEnabled 接口
		enabled := integration.IsEnabled()
		t.Logf("IsEnabled: %v", enabled)

		// 3. GetClientCount 接口
		clientCount := integration.GetClientCount()
		t.Logf("GetClientCount: %d", clientCount)

		// 4. GetConnectionStatus 接口
		connectionStatus := integration.GetConnectionStatus()
		t.Logf("GetConnectionStatus: %+v", connectionStatus)

		// 5. Close 接口
		err = integration.Close(ctx)
		if err != nil {
			t.Logf("Close failed: %v", err)
		} else {
			t.Logf("Close succeeded")
		}
	})
}

// TestUnifiedMCPIntegration_ConfigLoading 測試配置載入功能
func TestUnifiedMCPIntegration_ConfigLoading(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 測試配置載入
		config, err := mcp.LoadUnifiedMCPConfig(ctx)
		if err != nil {
			t.Logf("Failed to load unified MCP config: %v", err)
			return
		}

		if config == nil {
			t.Logf("No MCP configuration found")
			return
		}

		t.Logf("Loaded MCP config: enabled=%v, clients=%d",
			config.Enabled, len(config.Clients))

		// 測試配置詳細信息
		for i, client := range config.Clients {
			t.Logf("Client %d: name=%s, type=%s, url=%s, timeout=%v",
				i+1, client.Name, client.Type, client.ServerURL, client.Timeout)
		}
	})
}

// TestUnifiedMCPIntegration_PerformanceBaseline 測試性能基準
func TestUnifiedMCPIntegration_PerformanceBaseline(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 測試初始化性能
		startTime := time.Now()
		integration, err := mcp.NewUnifiedMCPIntegration(ctx)
		initDuration := time.Since(startTime)

		t.Logf("Initialization took: %v", initDuration)

		if err != nil {
			t.Logf("Failed to create unified MCP integration: %v", err)
			return
		}

		if integration == nil {
			t.Logf("MCP is disabled or not configured")
			return
		}

		// 測試工具定義獲取性能
		startTime = time.Now()
		tools, err := integration.GetToolDefinitions(ctx)
		toolDefDuration := time.Since(startTime)

		t.Logf("Tool definitions retrieval took: %v", toolDefDuration)

		if err != nil {
			t.Logf("Failed to get tool definitions: %v", err)
		} else {
			t.Logf("Retrieved %d tools in %v", len(tools), toolDefDuration)
		}

		// 測試健康檢查性能
		startTime = time.Now()
		healthCheck := integration.HealthCheck(ctx)
		healthCheckDuration := time.Since(startTime)

		t.Logf("Health check took: %v", healthCheckDuration)
		t.Logf("Health check result: %+v", healthCheck)

		// 清理
		startTime = time.Now()
		err = integration.Close(ctx)
		closeDuration := time.Since(startTime)

		t.Logf("Close took: %v", closeDuration)

		if err != nil {
			t.Logf("Failed to close integration: %v", err)
		}
	})
}

// TestUnifiedMCPIntegration_ErrorHandling 測試錯誤處理
func TestUnifiedMCPIntegration_ErrorHandling(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建統一 MCP 集成實例
		integration, err := mcp.NewUnifiedMCPIntegration(ctx)
		if err != nil {
			t.Logf("Failed to create unified MCP integration (expected if no config): %v", err)
			return
		}

		if integration == nil {
			t.Logf("MCP is disabled or not configured")
			return
		}

		// 測試無效工具調用
		result, err := integration.CallTool(ctx, "non_existent_tool", map[string]interface{}{
			"param1": "value1",
		})

		if err != nil {
			t.Logf("CallTool with invalid tool failed as expected: %v", err)
		} else if result != nil && !result.Success {
			t.Logf("CallTool with invalid tool returned error result as expected: %s", result.Error)
		}

		// 測試無效客戶端連接測試
		err = integration.TestConnection(ctx, "non_existent_client")
		if err != nil {
			t.Logf("TestConnection with invalid client failed as expected: %v", err)
		}

		// 清理
		err = integration.Close(ctx)
		if err != nil {
			t.Logf("Failed to close integration: %v", err)
		}
	})
}
