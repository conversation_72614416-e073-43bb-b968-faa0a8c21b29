package test

import (
	"context"
	"testing"

	"brainHub/internal/llms/mcp"
	"github.com/gogf/gf/v2/test/gtest"
)

// 測試用 Logger（符合 mcp.Logger 介面，輸出忽略）
type testLogger struct{}
func (l *testLogger) Warningf(ctx context.Context, format string, args ...interface{}) {}
func (l *testLogger) Errorf(ctx context.Context, format string, args ...interface{})   {}
func (l *testLogger) Debugf(ctx context.Context, format string, args ...interface{})   {}

// TestMCPIntegration 基本集成：禁用 MCP 避免實際連線，驗證初始化與查詢流程
func TestMCPIntegration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		config := &mcp.MCPConfig{Enabled: false}

		manager := mcp.NewMCPToolManager(config, &testLogger{})
		t.Assert<PERSON>(manager, nil)

		err := manager.Initialize(ctx)
		t.AssertNil(err)
		defer func() { _ = manager.Close(ctx) }()

		tools, err := manager.GetToolDefinitions(ctx)
		t.AssertNil(err)
		t.AssertEQ(len(tools), 0)

		status := manager.GetConnectionStatus()
		t.AssertEQ(len(status), 0)
	})
}

// TestMCPClientManager 基本客戶端管理：不建立任何伺服器設定
func TestMCPClientManager(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		config := &mcp.MCPConfig{Enabled: false}

		cm := mcp.NewMCPClientManager(config, &testLogger{})
		t.AssertNE(cm, nil)

		err := cm.Initialize(ctx)
		t.AssertNil(err)
		defer func() { _ = cm.Close(ctx) }()

		clients := cm.GetAllClients()
		t.AssertEQ(len(clients), 0)

		if _, ok := cm.GetClient("nonexistent"); ok {
			t.Fatalf("expected no client named 'nonexistent'")
		}

		status := cm.GetConnectionStatus()
		t.AssertEQ(len(status), 0)
	})
}

// TestMCPConfiguration 僅驗證結構與 ValidateConfig 行為
func TestMCPConfiguration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 有效配置（只做結構驗證，不連線）
		valid := &mcp.MCPConfig{
			Enabled: true,
			Servers: []mcp.MCPServerConfig{
				{ Name: "stdio-ok", Type: "stdio", Command: "bash", Args: []string{"-c", "true"}, Timeout: 5 },
				{ Name: "sse-ok", Type: "sse", URL: "http://localhost:9999/sse", Timeout: 5 },
				{ Name: "streamable-ok", Type: "streamable", URL: "http://localhost:9999/stream", Timeout: 5 },
			},
		}
		cm := mcp.NewConfigManager()
		// 只做 Validate，不會實際連線
		t.AssertNil(cm.ValidateConfig(valid))

		// 無效配置：缺少名稱 / 不支援類別 / 缺少必要欄位
		invalids := []*mcp.MCPConfig{
			{ Enabled: true, Servers: []mcp.MCPServerConfig{ { Name: "", Type: "stdio", Command: "bash" } } },
			{ Enabled: true, Servers: []mcp.MCPServerConfig{ { Name: "bad-type", Type: "unknown" } } },
			{ Enabled: true, Servers: []mcp.MCPServerConfig{ { Name: "sse-missing-url", Type: "sse" } } },
		}
		for _, cfg := range invalids {
			t.AssertNE(cm.ValidateConfig(cfg), nil)
		}
	})
}

