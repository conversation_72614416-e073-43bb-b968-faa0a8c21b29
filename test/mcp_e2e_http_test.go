package test

import (
	"context"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	bhmcp "brainHub/internal/llms/mcp"

	"github.com/gogf/gf/v2/test/gtest"
	mcpgo "github.com/modelcontextprotocol/go-sdk/mcp"
)

// buildTestServer 建立最小可用的 MCP Server，並註冊 echo 工具
// echo: 回傳輸入文字，供端到端驗證 ToolManager 解析 TextContent
func buildTestServer() *mcpgo.Server {
	srv := mcpgo.NewServer(&mcpgo.Implementation{Name: "e2e-server", Version: "v1.0.0"}, nil)

	type EchoInput struct {
		Text string `json:"text" jsonschema:"text to echo back"`
	}

	// 工具實作：回傳文字內容（TextContent），讓 ToolManager 能夠抽取為字串
	mcpgo.AddTool(srv, &mcpgo.Tool{
		Name:        "echo",
		Description: "Echo back the provided text",
	}, func(ctx context.Context, req *mcpgo.CallToolRequest, input EchoInput) (*mcpgo.CallToolResult, struct{}, error) {
		return &mcpgo.CallToolResult{
			Content: []mcpgo.Content{&mcpgo.TextContent{Text: "Echo: " + input.Text}},
		}, struct{}{}, nil
	})

	return srv
}

// TestMCP_E2E_HTTP 以 httptest.Server 驗證 SSE 與 Streamable 端到端流程
func TestMCP_E2E_HTTP(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		mux := http.NewServeMux()

		// 建立 Streamable Handler
		streamHandler := mcpgo.NewStreamableHTTPHandler(func(r *http.Request) *mcpgo.Server { return buildTestServer() }, nil)
		mux.Handle("/", streamHandler)

		// 啟動測試伺服器
		ts := httptest.NewServer(mux)
		defer ts.Close()

		// 構建 MCP 設定，指向我們的測試伺服器
		cfg := &bhmcp.MCPConfig{
			Enabled: true,
			Servers: []bhmcp.MCPServerConfig{
				{Name: "e2e-streamable", Type: bhmcp.TransportTypeStreamable, URL: ts.URL},
			},
			Global: bhmcp.MCPGlobalConfig{Timeout: 10},
		}

		mgr := bhmcp.NewMCPToolManager(cfg, &testLogger{})
		t.AssertNil(mgr.Initialize(ctx))
		defer mgr.Close(ctx)

		// 取得工具列表，應包含 echo 工具
		defs, err := mgr.GetToolDefinitions(ctx)
		t.AssertNil(err)

		names := make(map[string]bool)
		for _, d := range defs {
			names[d.Name] = true
		}
		t.Assert(names["e2e-streamable.echo"], true)

		// 呼叫 Streamable 的 echo 工具
		res2, err := mgr.CallTool(ctx, "e2e-streamable.echo", map[string]interface{}{"text": "world"})
		t.AssertNil(err)
		t.Assert(res2 != nil && res2.Success, true)
		t.Assert(strings.Contains(res2.Content, "Echo: world"), true)
	})
}
