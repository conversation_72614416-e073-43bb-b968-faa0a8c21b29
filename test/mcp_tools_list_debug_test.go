package test

import (
	"context"
	"testing"
	"time"

	"brainHub/internal/llms/mcp"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestMCPToolsList_Debug 專門測試 tools/list 功能
func TestMCPToolsList_Debug(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建客戶端配置
		clientConfig := &mcp.MCPClientConfig{
			Name:      "faceRecognition",
			Type:      "sse",
			ServerURL: "https://csai_uat_deepface.chainsea.com.tw/mcp/",
			Headers: map[string]string{
				"CS-API-Key": "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU",
			},
			Timeout:     60 * time.Second,
			MaxRetries:  3,
			AutoApprove: []string{},
			Disabled:    false,
		}

		// 創建統一 MCP 客戶端
		client := mcp.NewUnifiedMCPClient(clientConfig)
		t.AssertNE(client, nil)

		t.Logf("=== Testing tools/list functionality ===")
		t.Logf("Server URL: %s", clientConfig.ServerURL)
		t.Logf("Type: %s", clientConfig.Type)

		// 步驟 1：初始化客戶端
		t.Logf("Step 1: Initializing client...")
		err := client.Initialize(ctx)
		if err != nil {
			t.Logf("❌ Client initialization failed: %v", err)
			return
		}
		t.Logf("✅ Client initialization succeeded")

		// 步驟 2：檢查連接狀態
		t.Logf("Step 2: Checking connection status...")
		connected := client.IsConnected()
		t.Logf("Connected: %v", connected)
		if !connected {
			t.Logf("❌ Client not connected")
			return
		}
		t.Logf("✅ Client is connected")

		// 步驟 3：等待一段時間確保連接穩定
		t.Logf("Step 3: Waiting for connection to stabilize...")
		time.Sleep(2 * time.Second)

		// 步驟 4：嘗試獲取工具定義
		t.Logf("Step 4: Attempting to get tool definitions...")
		tools, err := client.GetToolDefinitions(ctx)
		if err != nil {
			t.Logf("❌ GetToolDefinitions failed: %v", err)
			
			// 檢查連接是否還活著
			stillConnected := client.IsConnected()
			t.Logf("Connection status after error: %v", stillConnected)
			
			// 檢查最後的錯誤
			if lastError := client.GetLastError(); lastError != nil {
				t.Logf("Last error: %v", lastError)
			}
		} else {
			t.Logf("✅ GetToolDefinitions succeeded: %d tools", len(tools))
			for i, tool := range tools {
				t.Logf("Tool %d: %s - %s", i+1, tool.Name, tool.Description)
			}
		}

		// 步驟 5：再次檢查連接狀態
		t.Logf("Step 5: Final connection status check...")
		finalConnected := client.IsConnected()
		t.Logf("Final connection status: %v", finalConnected)

		// 清理
		t.Logf("Step 6: Cleaning up...")
		err = client.Close()
		if err != nil {
			t.Logf("Failed to close client: %v", err)
		} else {
			t.Logf("Client closed successfully")
		}
	})
}

// TestMCPToolsList_MultipleAttempts 多次嘗試 tools/list
func TestMCPToolsList_MultipleAttempts(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建客戶端配置
		clientConfig := &mcp.MCPClientConfig{
			Name:      "faceRecognition",
			Type:      "sse",
			ServerURL: "https://csai_uat_deepface.chainsea.com.tw/mcp/",
			Headers: map[string]string{
				"CS-API-Key": "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU",
			},
			Timeout:     60 * time.Second,
			MaxRetries:  3,
			AutoApprove: []string{},
			Disabled:    false,
		}

		t.Logf("=== Testing multiple tools/list attempts ===")

		for attempt := 1; attempt <= 3; attempt++ {
			t.Logf("--- Attempt %d ---", attempt)
			
			// 創建新的客戶端實例
			client := mcp.NewUnifiedMCPClient(clientConfig)
			
			// 初始化
			err := client.Initialize(ctx)
			if err != nil {
				t.Logf("Attempt %d: Initialize failed: %v", attempt, err)
				continue
			}
			t.Logf("Attempt %d: Initialize succeeded", attempt)

			// 檢查連接
			connected := client.IsConnected()
			t.Logf("Attempt %d: Connected: %v", attempt, connected)

			if connected {
				// 嘗試獲取工具定義
				tools, err := client.GetToolDefinitions(ctx)
				if err != nil {
					t.Logf("Attempt %d: GetToolDefinitions failed: %v", attempt, err)
				} else {
					t.Logf("Attempt %d: GetToolDefinitions succeeded: %d tools", attempt, len(tools))
					if len(tools) > 0 {
						t.Logf("Attempt %d: First tool: %s", attempt, tools[0].Name)
					}
				}
			}

			// 清理
			client.Close()
			t.Logf("Attempt %d: Client closed", attempt)
			
			// 等待一段時間再進行下一次嘗試
			if attempt < 3 {
				time.Sleep(1 * time.Second)
			}
		}
	})
}

// TestMCPToolsList_WithTimeout 測試帶超時的 tools/list
func TestMCPToolsList_WithTimeout(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 創建帶超時的上下文
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		// 創建客戶端配置
		clientConfig := &mcp.MCPClientConfig{
			Name:      "faceRecognition",
			Type:      "sse",
			ServerURL: "https://csai_uat_deepface.chainsea.com.tw/mcp/",
			Headers: map[string]string{
				"CS-API-Key": "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU",
			},
			Timeout:     30 * time.Second, // 較短的超時時間
			MaxRetries:  1, // 只重試一次
			AutoApprove: []string{},
			Disabled:    false,
		}

		client := mcp.NewUnifiedMCPClient(clientConfig)
		t.AssertNE(client, nil)

		t.Logf("=== Testing tools/list with timeout ===")
		t.Logf("Context timeout: 30s")
		t.Logf("Client timeout: 30s")

		// 初始化
		err := client.Initialize(ctx)
		if err != nil {
			t.Logf("Initialize failed: %v", err)
			return
		}
		t.Logf("Initialize succeeded")

		// 檢查連接
		connected := client.IsConnected()
		t.Logf("Connected: %v", connected)

		if connected {
			// 嘗試獲取工具定義（帶超時）
			start := time.Now()
			tools, err := client.GetToolDefinitions(ctx)
			duration := time.Since(start)
			
			t.Logf("GetToolDefinitions took: %v", duration)
			
			if err != nil {
				t.Logf("GetToolDefinitions failed: %v", err)
				
				// 檢查是否是超時錯誤
				if ctx.Err() != nil {
					t.Logf("Context error: %v", ctx.Err())
				}
			} else {
				t.Logf("GetToolDefinitions succeeded: %d tools", len(tools))
			}
		}

		// 清理
		client.Close()
		t.Logf("Client closed")
	})
}

// TestMCPToolsList_ImmediateCall 立即調用 tools/list（不等待）
func TestMCPToolsList_ImmediateCall(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建客戶端配置
		clientConfig := &mcp.MCPClientConfig{
			Name:      "faceRecognition",
			Type:      "sse",
			ServerURL: "https://csai_uat_deepface.chainsea.com.tw/mcp/",
			Headers: map[string]string{
				"CS-API-Key": "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU",
			},
			Timeout:     60 * time.Second,
			MaxRetries:  3,
			AutoApprove: []string{},
			Disabled:    false,
		}

		client := mcp.NewUnifiedMCPClient(clientConfig)
		t.AssertNE(client, nil)

		t.Logf("=== Testing immediate tools/list call ===")

		// 初始化並立即調用 tools/list
		err := client.Initialize(ctx)
		if err != nil {
			t.Logf("Initialize failed: %v", err)
			return
		}
		t.Logf("Initialize succeeded")

		// 立即嘗試獲取工具定義（不等待）
		t.Logf("Calling GetToolDefinitions immediately...")
		tools, err := client.GetToolDefinitions(ctx)
		if err != nil {
			t.Logf("Immediate GetToolDefinitions failed: %v", err)
		} else {
			t.Logf("Immediate GetToolDefinitions succeeded: %d tools", len(tools))
		}

		// 清理
		client.Close()
		t.Logf("Client closed")
	})
}
