2025-07-07T16:46:50.015+0800	WARN	config_client/config_client.go:335	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-07T16:46:50.015+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-07T16:46:50.015+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-07T16:46:50.015+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-013a79bc-66d0-43e4-a343-50950aa30bcf)
2025-07-07T16:46:50.015+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:46:50.015+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:46:50.015+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:46:50.015+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-013a79bc-66d0-43e4-a343-50950aa30bcf try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:46:50.015+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:46:50.015+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:46:50.015+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:46:50.140+0800	INFO	rpc/rpc_client.go:337	config-0-013a79bc-66d0-43e4-a343-50950aa30bcf success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878010126_192.168.3.3_61362
2025-07-07T16:46:50.140+0800	INFO	rpc/rpc_client.go:486	config-0-013a79bc-66d0-43e4-a343-50950aa30bcf notify connected event to listeners , connectionId=1751878010126_192.168.3.3_61362
2025-07-07T16:46:50.141+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-07-07T16:53:51.930+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-07T16:53:51.931+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-07T16:53:51.931+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-ca3d12db-3993-498a-9f78-16d63b862c72)
2025-07-07T16:53:51.931+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:53:51.931+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:53:51.931+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:53:51.931+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-ca3d12db-3993-498a-9f78-16d63b862c72 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:53:51.931+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:53:51.931+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:53:51.931+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:53:52.049+0800	INFO	rpc/rpc_client.go:337	config-0-ca3d12db-3993-498a-9f78-16d63b862c72 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878432012_192.168.3.3_62029
2025-07-07T16:53:52.050+0800	INFO	rpc/rpc_client.go:486	config-0-ca3d12db-3993-498a-9f78-16d63b862c72 notify connected event to listeners , connectionId=1751878432012_192.168.3.3_62029
2025-07-07T16:53:52.050+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-07-07T16:54:39.201+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-07T16:54:39.202+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-07T16:54:39.202+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-a7219eea-a68f-4213-a629-05adb2195eb6)
2025-07-07T16:54:39.202+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:54:39.202+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:54:39.202+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:54:39.202+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-a7219eea-a68f-4213-a629-05adb2195eb6 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:54:39.202+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:54:39.202+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:54:39.202+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:54:39.326+0800	INFO	rpc/rpc_client.go:337	config-0-a7219eea-a68f-4213-a629-05adb2195eb6 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878479289_192.168.3.3_62130
2025-07-07T16:54:39.329+0800	INFO	rpc/rpc_client.go:486	config-0-a7219eea-a68f-4213-a629-05adb2195eb6 notify connected event to listeners , connectionId=1751878479289_192.168.3.3_62130
2025-07-07T16:54:39.329+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-07-07T16:55:24.521+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-07T16:55:24.521+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-07T16:55:24.522+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-625567c4-e98f-4d33-9f71-914ae9f7d81d)
2025-07-07T16:55:24.522+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:55:24.522+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:55:24.522+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:55:24.522+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-625567c4-e98f-4d33-9f71-914ae9f7d81d try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:55:24.522+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:55:24.522+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:55:24.522+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:55:24.647+0800	INFO	rpc/rpc_client.go:337	config-0-625567c4-e98f-4d33-9f71-914ae9f7d81d success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878524610_192.168.3.3_62221
2025-07-07T16:55:24.647+0800	INFO	rpc/rpc_client.go:486	config-0-625567c4-e98f-4d33-9f71-914ae9f7d81d notify connected event to listeners , connectionId=1751878524610_192.168.3.3_62221
2025-07-07T16:55:24.647+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-07-07T16:56:32.397+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-07T16:56:32.398+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-07T16:56:32.398+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-ba565615-185a-4b7a-8061-04975ca6fc89)
2025-07-07T16:56:32.398+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:56:32.398+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:56:32.398+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:56:32.398+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-ba565615-185a-4b7a-8061-04975ca6fc89 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:56:32.398+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:56:32.398+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:56:32.398+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:56:32.522+0800	INFO	rpc/rpc_client.go:337	config-0-ba565615-185a-4b7a-8061-04975ca6fc89 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878592485_192.168.3.3_62342
2025-07-07T16:56:32.523+0800	INFO	rpc/rpc_client.go:486	config-0-ba565615-185a-4b7a-8061-04975ca6fc89 notify connected event to listeners , connectionId=1751878592485_192.168.3.3_62342
2025-07-07T16:56:32.523+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-07-17T16:45:12.686+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-17T16:45:12.687+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-17T16:45:12.687+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-771d20a5-909c-46f9-8376-db278de855db)
2025-07-17T16:45:12.687+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-17T16:45:12.687+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-17T16:45:12.687+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-17T16:45:12.687+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-771d20a5-909c-46f9-8376-db278de855db try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-17T16:45:12.687+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-17T16:45:12.687+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-17T16:45:12.687+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-17T16:45:12.803+0800	INFO	rpc/rpc_client.go:337	config-0-771d20a5-909c-46f9-8376-db278de855db success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1752741912782_192.168.3.3_52946
2025-07-17T16:45:12.804+0800	INFO	rpc/rpc_client.go:486	config-0-771d20a5-909c-46f9-8376-db278de855db notify connected event to listeners , connectionId=1752741912782_192.168.3.3_52946
2025-07-17T16:45:12.804+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-08-05T14:04:22.952+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-08-05T14:04:22.953+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-08-05T14:04:22.953+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-49570744-85e8-422c-8ed5-6dbb153b0a40)
2025-08-05T14:04:22.953+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-08-05T14:04:22.953+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-08-05T14:04:22.953+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-08-05T14:04:22.953+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-49570744-85e8-422c-8ed5-6dbb153b0a40 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-08-05T14:04:22.953+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-05T14:04:22.953+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-05T14:04:22.953+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-05T14:04:23.077+0800	INFO	rpc/rpc_client.go:337	config-0-49570744-85e8-422c-8ed5-6dbb153b0a40 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1754373863006_192.168.3.5_60930
2025-08-05T14:04:23.077+0800	INFO	rpc/rpc_client.go:486	config-0-49570744-85e8-422c-8ed5-6dbb153b0a40 notify connected event to listeners , connectionId=1754373863006_192.168.3.5_60930
2025-08-05T14:04:23.077+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T16:06:01.745+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T16:06:01.746+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T16:06:01.746+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-d9d7aea2-1b3b-4038-8c89-bf35eb497b17)
2025-09-17T16:06:01.746+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T16:06:01.746+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T16:06:01.746+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T16:06:01.746+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-d9d7aea2-1b3b-4038-8c89-bf35eb497b17 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T16:06:01.746+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T16:06:01.746+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T16:06:01.746+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T16:06:01.866+0800	INFO	rpc/rpc_client.go:337	config-0-d9d7aea2-1b3b-4038-8c89-bf35eb497b17 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758096361850_192.168.3.254_60950
2025-09-17T16:06:01.866+0800	INFO	rpc/rpc_client.go:486	config-0-d9d7aea2-1b3b-4038-8c89-bf35eb497b17 notify connected event to listeners , connectionId=1758096361850_192.168.3.254_60950
2025-09-17T16:06:01.866+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T16:10:55.968+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T16:10:55.969+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T16:10:55.969+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-db564c8b-c359-4139-a313-98733f0d0ea8)
2025-09-17T16:10:55.969+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T16:10:55.969+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T16:10:55.969+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T16:10:55.969+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-db564c8b-c359-4139-a313-98733f0d0ea8 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T16:10:55.969+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T16:10:55.969+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T16:10:55.969+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T16:10:56.096+0800	INFO	rpc/rpc_client.go:337	config-0-db564c8b-c359-4139-a313-98733f0d0ea8 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758096656079_192.168.3.254_61629
2025-09-17T16:10:56.096+0800	INFO	rpc/rpc_client.go:486	config-0-db564c8b-c359-4139-a313-98733f0d0ea8 notify connected event to listeners , connectionId=1758096656079_192.168.3.254_61629
2025-09-17T16:10:56.097+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T16:11:42.430+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T16:11:42.430+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T16:11:42.430+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-6f990cb1-a013-49af-a4f8-d60df611c252)
2025-09-17T16:11:42.430+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T16:11:42.430+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T16:11:42.430+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T16:11:42.430+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-6f990cb1-a013-49af-a4f8-d60df611c252 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T16:11:42.431+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T16:11:42.431+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T16:11:42.431+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T16:11:42.551+0800	INFO	rpc/rpc_client.go:337	config-0-6f990cb1-a013-49af-a4f8-d60df611c252 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758096702533_192.168.3.254_61752
2025-09-17T16:11:42.551+0800	INFO	rpc/rpc_client.go:486	config-0-6f990cb1-a013-49af-a4f8-d60df611c252 notify connected event to listeners , connectionId=1758096702533_192.168.3.254_61752
2025-09-17T16:11:42.551+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T16:12:10.629+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T16:12:10.629+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T16:12:10.629+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-523e2839-346b-4b10-be64-55d74abf4c0d)
2025-09-17T16:12:10.629+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T16:12:10.629+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T16:12:10.629+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T16:12:10.629+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-523e2839-346b-4b10-be64-55d74abf4c0d try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T16:12:10.630+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T16:12:10.630+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T16:12:10.630+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T16:12:10.753+0800	INFO	rpc/rpc_client.go:337	config-0-523e2839-346b-4b10-be64-55d74abf4c0d success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758096730738_192.168.3.254_61825
2025-09-17T16:12:10.754+0800	INFO	rpc/rpc_client.go:486	config-0-523e2839-346b-4b10-be64-55d74abf4c0d notify connected event to listeners , connectionId=1758096730738_192.168.3.254_61825
2025-09-17T16:12:10.754+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T16:14:51.369+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T16:14:51.369+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T16:14:51.369+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-87a7cc62-4470-4c26-a7d2-2d63ee5030d0)
2025-09-17T16:14:51.369+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T16:14:51.369+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T16:14:51.369+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T16:14:51.369+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-87a7cc62-4470-4c26-a7d2-2d63ee5030d0 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T16:14:51.369+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T16:14:51.369+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T16:14:51.369+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T16:14:51.493+0800	INFO	rpc/rpc_client.go:337	config-0-87a7cc62-4470-4c26-a7d2-2d63ee5030d0 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758096891475_192.168.3.254_62045
2025-09-17T16:14:51.494+0800	INFO	rpc/rpc_client.go:486	config-0-87a7cc62-4470-4c26-a7d2-2d63ee5030d0 notify connected event to listeners , connectionId=1758096891475_192.168.3.254_62045
2025-09-17T16:14:51.494+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T17:04:21.336+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T17:04:21.336+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T17:04:21.336+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-890aaa6d-2c46-40ca-8559-882e54e12e4f)
2025-09-17T17:04:21.336+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T17:04:21.336+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T17:04:21.336+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T17:04:21.336+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-890aaa6d-2c46-40ca-8559-882e54e12e4f try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T17:04:21.336+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T17:04:21.336+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T17:04:21.336+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T17:04:21.454+0800	INFO	rpc/rpc_client.go:337	config-0-890aaa6d-2c46-40ca-8559-882e54e12e4f success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758099861421_192.168.3.254_64887
2025-09-17T17:04:21.455+0800	INFO	rpc/rpc_client.go:486	config-0-890aaa6d-2c46-40ca-8559-882e54e12e4f notify connected event to listeners , connectionId=1758099861421_192.168.3.254_64887
2025-09-17T17:04:21.455+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T17:05:02.403+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T17:05:02.403+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T17:05:02.403+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-72529dc3-5335-4009-b31c-5cbf6f821a39)
2025-09-17T17:05:02.403+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T17:05:02.403+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T17:05:02.403+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T17:05:02.403+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-72529dc3-5335-4009-b31c-5cbf6f821a39 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T17:05:02.403+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T17:05:02.403+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T17:05:02.403+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T17:05:02.526+0800	INFO	rpc/rpc_client.go:337	config-0-72529dc3-5335-4009-b31c-5cbf6f821a39 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758099902492_192.168.3.254_64978
2025-09-17T17:05:02.526+0800	INFO	rpc/rpc_client.go:486	config-0-72529dc3-5335-4009-b31c-5cbf6f821a39 notify connected event to listeners , connectionId=1758099902492_192.168.3.254_64978
2025-09-17T17:05:02.526+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T18:28:17.238+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T18:28:17.239+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T18:28:17.239+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-8118a386-c270-4eff-9ed2-e8f85ea25ee2)
2025-09-17T18:28:17.239+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T18:28:17.239+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T18:28:17.239+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T18:28:17.239+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-8118a386-c270-4eff-9ed2-e8f85ea25ee2 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T18:28:17.239+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T18:28:17.239+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T18:28:17.239+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T18:28:17.358+0800	INFO	rpc/rpc_client.go:337	config-0-8118a386-c270-4eff-9ed2-e8f85ea25ee2 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758104897310_192.168.3.254_54861
2025-09-17T18:28:17.359+0800	INFO	rpc/rpc_client.go:486	config-0-8118a386-c270-4eff-9ed2-e8f85ea25ee2 notify connected event to listeners , connectionId=1758104897310_192.168.3.254_54861
2025-09-17T18:28:17.359+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
