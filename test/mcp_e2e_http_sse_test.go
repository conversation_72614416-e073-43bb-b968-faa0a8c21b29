package test

import (
	"context"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	bhmcp "brainHub/internal/llms/mcp"

	"github.com/gogf/gf/v2/test/gtest"
	mcpgo "github.com/modelcontextprotocol/go-sdk/mcp"
)

// buildSSETestServer creates a minimal MCP server exposing a simple echo tool.
func buildSSETestServer() *mcpgo.Server {
	srv := mcpgo.NewServer(&mcpgo.Implementation{Name: "e2e-sse-server", Version: "v1.0.0"}, nil)
	type EchoInput struct {
		Text string `json:"text" jsonschema:"text to echo back"`
	}
	mcpgo.AddTool(srv, &mcpgo.Tool{
		Name:        "echo",
		Description: "Echo back the provided text",
	}, func(ctx context.Context, req *mcpgo.CallToolRequest, input EchoInput) (*mcpgo.CallToolResult, struct{}, error) {
		return &mcpgo.CallToolResult{Content: []mcpgo.Content{&mcpgo.TextContent{Text: "Echo: " + input.Text}}}, struct{}{}, nil
	})
	return srv
}

func TestMCP_E2E_HTTP_SSE(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		mux := http.NewServeMux()
		// SSE handler from go-sdk (main branch provides SSE support)
		sseHandler := mcpgo.NewSSEHandler(func(r *http.Request) *mcpgo.Server { return buildSSETestServer() })
		mux.Handle("/", sseHandler)

		ts := httptest.NewServer(mux)
		defer ts.Close()

		cfg := &bhmcp.MCPConfig{
			Enabled: true,
			Servers: []bhmcp.MCPServerConfig{
				{Name: "e2e-sse", Type: bhmcp.TransportTypeSSE, URL: ts.URL},
			},
			Global: bhmcp.MCPGlobalConfig{Timeout: 10},
		}

		mgr := bhmcp.NewMCPToolManager(cfg, &testLogger{})
		t.AssertNil(mgr.Initialize(ctx))
		defer mgr.Close(ctx)

		defs, err := mgr.GetToolDefinitions(ctx)
		t.AssertNil(err)
		names := map[string]bool{}
		for _, d := range defs {
			names[d.Name] = true
		}
		t.Assert(names["e2e-sse.echo"], true)

		res, err := mgr.CallTool(ctx, "e2e-sse.echo", map[string]interface{}{"text": "sse"})
		t.AssertNil(err)
		t.Assert(res != nil && res.Success, true)
		t.Assert(strings.Contains(res.Content, "Echo: sse"), true)
	})
}
