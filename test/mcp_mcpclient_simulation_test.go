package test

import (
	"context"
	"testing"
	"time"

	"brainHub/internal/llms/mcp"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestMCPClient_SimulateExactBehavior 模擬 mcpClient 的確切行為
func TestMCPClient_SimulateExactBehavior(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 完全按照 mcpClient main.go 的配置
		const (
			MCPServerURL      = "https://csai_uat_deepface.chainsea.com.tw/mcp/"
			APIKeyHeader      = "CS-API-Key"
			APIKeyValue       = "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU"
			ConnectionTimeout = 60 * time.Second
		)

		t.Logf("=== Simulating mcpClient Exact Behavior ===")
		t.Logf("Server URL: %s", MCPServerURL)
		t.Logf("API Key: %s", APIKeyValue)

		// 使用 brainHub 的統一 MCP 客戶端（模擬 mcpClient 的配置）
		clientConfig := &mcp.MCPClientConfig{
			Name:      "mcpClient-simulation",
			Type:      "sse", // mcpClient 使用 SSE
			ServerURL: MCPServerURL,
			Headers: map[string]string{
				APIKeyHeader: APIKeyValue,
			},
			Timeout:     ConnectionTimeout,
			MaxRetries:  3,
			AutoApprove: []string{},
			Disabled:    false,
		}

		t.Logf("Step 1: Creating unified MCP client...")
		client := mcp.NewUnifiedMCPClient(clientConfig)
		if client == nil {
			t.Logf("❌ Failed to create unified MCP client")
			return
		}
		t.Logf("✅ Unified MCP client created")

		t.Logf("Step 2: Initializing client...")
		err := client.Initialize(ctx)
		if err != nil {
			t.Logf("❌ Failed to initialize client: %v", err)
			return
		}
		t.Logf("✅ Client initialized successfully")

		// 確保客戶端關閉
		defer func() {
			if client != nil {
				client.Close()
				t.Logf("MCP client closed")
			}
		}()

		t.Logf("Step 3: Checking connection status...")
		connected := client.IsConnected()
		t.Logf("Connected: %v", connected)
		
		if !connected {
			t.Logf("❌ Client not connected")
			return
		}

		t.Logf("Step 4: Calling tools/list (GetToolDefinitions)...")
		
		// 調用 GetToolDefinitions（這是關鍵測試）
		tools, err := client.GetToolDefinitions(ctx)
		if err != nil {
			t.Logf("❌ GetToolDefinitions failed: %v", err)
			
			// 檢查錯誤類型
			t.Logf("Error type: %T", err)
			t.Logf("Error details: %+v", err)
			
			// 檢查是否是連接問題
			stillConnected := client.IsConnected()
			t.Logf("Still connected after error: %v", stillConnected)
			
			return
		}
		
		t.Logf("✅ GetToolDefinitions succeeded!")
		t.Logf("Number of tools: %d", len(tools))
		
		for i, tool := range tools {
			t.Logf("Tool %d:", i+1)
			t.Logf("  Name: %s", tool.Name)
			t.Logf("  Description: %s", tool.Description)
			if tool.Parameters != nil {
				t.Logf("  Has parameters: yes")
			} else {
				t.Logf("  Has parameters: no")
			}
		}

		// 如果有工具，嘗試調用第一個
		if len(tools) > 0 {
			firstTool := tools[0]
			t.Logf("Step 5: Testing tool call with '%s'...", firstTool.Name)
			
			// 嘗試調用工具（使用空參數）
			callResult, err := client.CallTool(ctx, firstTool.Name, map[string]interface{}{})
			
			if err != nil {
				t.Logf("❌ Tool call failed: %v", err)
			} else {
				t.Logf("✅ Tool call succeeded!")
				t.Logf("Result: %+v", callResult)
			}
		}
	})
}

// TestMCPClient_CompareImplementations 比較不同實現方式
func TestMCPClient_CompareImplementations(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		t.Logf("=== Comparing MCP Implementations ===")

		// 測試配置
		configs := []struct {
			name   string
			config *mcp.MCPClientConfig
		}{
			{
				name: "SSE-with-headers",
				config: &mcp.MCPClientConfig{
					Name:      "sse-test",
					Type:      "sse",
					ServerURL: "https://csai_uat_deepface.chainsea.com.tw/mcp/",
					Headers: map[string]string{
						"CS-API-Key": "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU",
					},
					Timeout:     30 * time.Second,
					MaxRetries:  1,
					AutoApprove: []string{},
					Disabled:    false,
				},
			},
			{
				name: "Streamable-with-headers",
				config: &mcp.MCPClientConfig{
					Name:      "streamable-test",
					Type:      "streamable",
					ServerURL: "https://csai_uat_deepface.chainsea.com.tw/mcp/",
					Headers: map[string]string{
						"CS-API-Key": "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU",
					},
					Timeout:     30 * time.Second,
					MaxRetries:  1,
					AutoApprove: []string{},
					Disabled:    false,
				},
			},
		}

		for _, tc := range configs {
			t.Logf("--- Testing %s ---", tc.name)
			
			client := mcp.NewUnifiedMCPClient(tc.config)
			
			// 初始化
			err := client.Initialize(ctx)
			if err != nil {
				t.Logf("%s: Initialize failed: %v", tc.name, err)
				continue
			}
			t.Logf("%s: Initialize succeeded", tc.name)

			// 檢查連接
			connected := client.IsConnected()
			t.Logf("%s: Connected: %v", tc.name, connected)

			if connected {
				// 測試工具列表
				tools, err := client.GetToolDefinitions(ctx)
				if err != nil {
					t.Logf("%s: GetToolDefinitions failed: %v", tc.name, err)
				} else {
					t.Logf("%s: GetToolDefinitions succeeded: %d tools", tc.name, len(tools))
					if len(tools) > 0 {
						t.Logf("%s: First tool: %s", tc.name, tools[0].Name)
					}
				}
			}

			client.Close()
			t.Logf("%s: Client closed", tc.name)
		}
	})
}

// TestMCPClient_DebugConnection 調試連接問題
func TestMCPClient_DebugConnection(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		t.Logf("=== Debugging MCP Connection ===")

		clientConfig := &mcp.MCPClientConfig{
			Name:      "debug-client",
			Type:      "sse",
			ServerURL: "https://csai_uat_deepface.chainsea.com.tw/mcp/",
			Headers: map[string]string{
				"CS-API-Key": "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU",
			},
			Timeout:     60 * time.Second,
			MaxRetries:  3,
			AutoApprove: []string{},
			Disabled:    false,
		}

		client := mcp.NewUnifiedMCPClient(clientConfig)
		
		t.Logf("Config: %+v", clientConfig)
		
		// 初始化
		t.Logf("Initializing...")
		err := client.Initialize(ctx)
		if err != nil {
			t.Logf("Initialize failed: %v", err)
			return
		}
		t.Logf("Initialize succeeded")

		// 檢查連接狀態
		connected := client.IsConnected()
		t.Logf("Connected: %v", connected)

		if connected {
			// 立即嘗試獲取工具
			t.Logf("Immediately calling GetToolDefinitions...")
			tools, err := client.GetToolDefinitions(ctx)
			if err != nil {
				t.Logf("Immediate call failed: %v", err)
			} else {
				t.Logf("Immediate call succeeded: %d tools", len(tools))
			}

			// 等待一段時間後再試
			t.Logf("Waiting 3 seconds...")
			time.Sleep(3 * time.Second)
			
			t.Logf("Calling GetToolDefinitions after wait...")
			tools, err = client.GetToolDefinitions(ctx)
			if err != nil {
				t.Logf("Delayed call failed: %v", err)
			} else {
				t.Logf("Delayed call succeeded: %d tools", len(tools))
			}
		}

		client.Close()
		t.Logf("Debug session completed")
	})
}
