package test

import (
	"io"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestMCPServer_Investigation 調查 MCP 服務器的實際行為
func TestMCPServer_Investigation(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		serverURL := "https://csai_uat_deepface.chainsea.com.tw/mcp/"
		apiKey := "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU"

		t.Logf("=== Investigating MCP Server ===")
		t.Logf("Server URL: %s", serverURL)

		// 測試 1：基本 HTTP GET 請求
		t.Logf("--- Test 1: Basic HTTP GET ---")
		client := &http.Client{Timeout: 10 * time.Second}

		req, err := http.NewRequest("GET", serverURL, nil)
		if err != nil {
			t.Logf("Failed to create request: %v", err)
			return
		}

		req.Header.Set("CS-API-Key", apiKey)
		req.Header.Set("User-Agent", "brainHub-MCP-Client/1.0")

		resp, err := client.Do(req)
		if err != nil {
			t.Logf("HTTP GET failed: %v", err)
		} else {
			defer resp.Body.Close()
			body, _ := io.ReadAll(resp.Body)
			t.Logf("HTTP GET Response:")
			t.Logf("  Status: %s", resp.Status)
			t.Logf("  Headers: %v", resp.Header)
			t.Logf("  Body: %s", string(body))
		}

		// 測試 2：檢查是否支持 SSE
		t.Logf("--- Test 2: SSE Connection Test ---")
		req2, err := http.NewRequest("GET", serverURL, nil)
		if err != nil {
			t.Logf("Failed to create SSE request: %v", err)
			return
		}

		req2.Header.Set("CS-API-Key", apiKey)
		req2.Header.Set("Accept", "text/event-stream")
		req2.Header.Set("Cache-Control", "no-cache")
		req2.Header.Set("Connection", "keep-alive")

		resp2, err := client.Do(req2)
		if err != nil {
			t.Logf("SSE request failed: %v", err)
		} else {
			defer resp2.Body.Close()
			t.Logf("SSE Response:")
			t.Logf("  Status: %s", resp2.Status)
			t.Logf("  Content-Type: %s", resp2.Header.Get("Content-Type"))
			t.Logf("  Headers: %v", resp2.Header)

			// 讀取前幾行 SSE 數據
			body2, _ := io.ReadAll(resp2.Body)
			bodyStr := string(body2)
			if len(bodyStr) > 500 {
				bodyStr = bodyStr[:500] + "..."
			}
			t.Logf("  Body: %s", bodyStr)
		}

		// 測試 3：嘗試不同的端點
		t.Logf("--- Test 3: Different Endpoints ---")
		endpoints := []string{
			serverURL + "tools",
			serverURL + "tools/list",
			serverURL + "api/tools",
			serverURL + "mcp/tools",
			strings.TrimSuffix(serverURL, "/") + "/tools",
		}

		for _, endpoint := range endpoints {
			t.Logf("Testing endpoint: %s", endpoint)
			req3, err := http.NewRequest("GET", endpoint, nil)
			if err != nil {
				t.Logf("  Failed to create request: %v", err)
				continue
			}

			req3.Header.Set("CS-API-Key", apiKey)

			resp3, err := client.Do(req3)
			if err != nil {
				t.Logf("  Request failed: %v", err)
			} else {
				defer resp3.Body.Close()
				body3, _ := io.ReadAll(resp3.Body)
				bodyStr3 := string(body3)
				if len(bodyStr3) > 200 {
					bodyStr3 = bodyStr3[:200] + "..."
				}
				t.Logf("  Status: %s, Body: %s", resp3.Status, bodyStr3)
			}
		}

		// 測試 4：POST 請求測試
		t.Logf("--- Test 4: POST Request Test ---")
		postData := `{"jsonrpc": "2.0", "id": 1, "method": "tools/list", "params": {}}`
		req4, err := http.NewRequest("POST", serverURL, strings.NewReader(postData))
		if err != nil {
			t.Logf("Failed to create POST request: %v", err)
			return
		}

		req4.Header.Set("CS-API-Key", apiKey)
		req4.Header.Set("Content-Type", "application/json")

		resp4, err := client.Do(req4)
		if err != nil {
			t.Logf("POST request failed: %v", err)
		} else {
			defer resp4.Body.Close()
			body4, _ := io.ReadAll(resp4.Body)
			t.Logf("POST Response:")
			t.Logf("  Status: %s", resp4.Status)
			t.Logf("  Body: %s", string(body4))
		}
	})
}

// TestMCPServer_APIDocumentation 測試是否有 API 文檔端點
func TestMCPServer_APIDocumentation(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		serverURL := "https://csai_uat_deepface.chainsea.com.tw/mcp/"
		apiKey := "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU"

		t.Logf("=== Testing API Documentation Endpoints ===")

		client := &http.Client{Timeout: 10 * time.Second}

		// 常見的文檔端點
		docEndpoints := []string{
			serverURL + "docs",
			serverURL + "swagger",
			serverURL + "api-docs",
			serverURL + "openapi.json",
			serverURL + "api.json",
			strings.TrimSuffix(serverURL, "/") + "/docs",
			strings.TrimSuffix(serverURL, "/") + "/swagger",
			"https://csai_uat_deepface.chainsea.com.tw/docs",
			"https://csai_uat_deepface.chainsea.com.tw/swagger",
			"https://csai_uat_deepface.chainsea.com.tw/api-docs",
		}

		for _, endpoint := range docEndpoints {
			t.Logf("Testing documentation endpoint: %s", endpoint)
			req, err := http.NewRequest("GET", endpoint, nil)
			if err != nil {
				t.Logf("  Failed to create request: %v", err)
				continue
			}

			req.Header.Set("CS-API-Key", apiKey)

			resp, err := client.Do(req)
			if err != nil {
				t.Logf("  Request failed: %v", err)
			} else {
				defer resp.Body.Close()
				t.Logf("  Status: %s, Content-Type: %s", resp.Status, resp.Header.Get("Content-Type"))

				if resp.StatusCode == 200 {
					body, _ := io.ReadAll(resp.Body)
					bodyStr := string(body)
					if len(bodyStr) > 300 {
						bodyStr = bodyStr[:300] + "..."
					}
					t.Logf("  Body preview: %s", bodyStr)
				}
			}
		}
	})
}

// TestMCPServer_HealthCheck 測試健康檢查端點
func TestMCPServer_HealthCheck(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		serverURL := "https://csai_uat_deepface.chainsea.com.tw/mcp/"
		apiKey := "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU"

		t.Logf("=== Testing Health Check Endpoints ===")

		client := &http.Client{Timeout: 10 * time.Second}

		// 常見的健康檢查端點
		healthEndpoints := []string{
			serverURL + "health",
			serverURL + "ping",
			serverURL + "status",
			strings.TrimSuffix(serverURL, "/") + "/health",
			strings.TrimSuffix(serverURL, "/") + "/ping",
			"https://csai_uat_deepface.chainsea.com.tw/health",
			"https://csai_uat_deepface.chainsea.com.tw/ping",
		}

		for _, endpoint := range healthEndpoints {
			t.Logf("Testing health endpoint: %s", endpoint)
			req, err := http.NewRequest("GET", endpoint, nil)
			if err != nil {
				t.Logf("  Failed to create request: %v", err)
				continue
			}

			req.Header.Set("CS-API-Key", apiKey)

			resp, err := client.Do(req)
			if err != nil {
				t.Logf("  Request failed: %v", err)
			} else {
				defer resp.Body.Close()
				body, _ := io.ReadAll(resp.Body)
				t.Logf("  Status: %s, Body: %s", resp.Status, string(body))
			}
		}
	})
}

// TestMCPServer_RealMCPProtocol 測試真正的 MCP 協議
func TestMCPServer_RealMCPProtocol(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		t.Logf("=== Testing Real MCP Protocol ===")

		// 這個測試用來確認服務器是否真的支持 MCP 協議
		// 或者它只是一個普通的 REST API

		serverURL := "https://csai_uat_deepface.chainsea.com.tw/mcp/"
		apiKey := "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU"

		client := &http.Client{Timeout: 10 * time.Second}

		// 測試 MCP 初始化請求
		initData := `{
			"jsonrpc": "2.0",
			"id": 1,
			"method": "initialize",
			"params": {
				"protocolVersion": "2024-11-05",
				"capabilities": {
					"tools": {}
				},
				"clientInfo": {
					"name": "brainHub",
					"version": "1.0.0"
				}
			}
		}`

		t.Logf("Sending MCP initialize request...")
		req, err := http.NewRequest("POST", serverURL, strings.NewReader(initData))
		if err != nil {
			t.Logf("Failed to create initialize request: %v", err)
			return
		}

		req.Header.Set("CS-API-Key", apiKey)
		req.Header.Set("Content-Type", "application/json")

		resp, err := client.Do(req)
		if err != nil {
			t.Logf("Initialize request failed: %v", err)
		} else {
			defer resp.Body.Close()
			body, _ := io.ReadAll(resp.Body)
			t.Logf("Initialize Response:")
			t.Logf("  Status: %s", resp.Status)
			t.Logf("  Body: %s", string(body))
		}

		// 測試 tools/list 請求
		toolsData := `{
			"jsonrpc": "2.0",
			"id": 2,
			"method": "tools/list",
			"params": {}
		}`

		t.Logf("Sending tools/list request...")
		req2, err := http.NewRequest("POST", serverURL, strings.NewReader(toolsData))
		if err != nil {
			t.Logf("Failed to create tools/list request: %v", err)
			return
		}

		req2.Header.Set("CS-API-Key", apiKey)
		req2.Header.Set("Content-Type", "application/json")

		resp2, err := client.Do(req2)
		if err != nil {
			t.Logf("Tools/list request failed: %v", err)
		} else {
			defer resp2.Body.Close()
			body2, _ := io.ReadAll(resp2.Body)
			t.Logf("Tools/list Response:")
			t.Logf("  Status: %s", resp2.Status)
			t.Logf("  Body: %s", string(body2))
		}
	})
}
