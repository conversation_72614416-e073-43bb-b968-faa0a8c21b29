package test

import (
	"context"
	"testing"

	"brainHub/internal/llms/aoai"
	"brainHub/internal/llms/claude"
	"brainHub/internal/llms/gemini"
	"brainHub/internal/llms/mcp"
	"brainHub/internal/model/llm"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestAoAi_MCPIntegration 測試 Azure OpenAI 與 MCP 的集成
func TestAoAi_MCPIntegration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建 Azure OpenAI 實例
		aoaiLLM := aoai.New()
		t.AssertNE(aoaiLLM, nil)

		// 創建測試配置（MCP 配置現在直接從配置文件讀取）
		config := &llm.LLMsConfig{
			// MCP 配置已移除，現在由 ConfigManager 直接從配置文件讀取
		}

		// 創建測試 payload
		payload := &llm.Payload{
			SystemInstruction: "You are a helpful assistant.",
		}

		// 測試初始化（應該成功，即使沒有有效的 Azure OpenAI 配置）
		err := aoaiLLM.Initialize(ctx, config, payload)
		if err != nil {
			t.Logf("Initialize failed as expected (no valid Azure OpenAI config): %v", err)
		}

		// 清理
		aoaiLLM.Release(ctx)
	})
}

// TestClaude_MCPIntegration 測試 Claude 與 MCP 的集成
func TestClaude_MCPIntegration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建 Claude 實例
		claudeLLM := claude.New()
		t.AssertNE(claudeLLM, nil)

		// 創建測試配置（MCP 配置現在直接從配置文件讀取）
		config := &llm.LLMsConfig{
			// MCP 配置已移除，現在由 ConfigManager 直接從配置文件讀取
		}

		// 創建測試 payload
		payload := &llm.Payload{
			SystemInstruction: "You are a helpful assistant.",
		}

		// 測試初始化
		err := claudeLLM.Initialize(ctx, config, payload)
		if err != nil {
			t.Logf("Initialize failed as expected (no valid Claude config): %v", err)
		}

		// 清理
		claudeLLM.Release(ctx)
	})
}

// TestGemini_MCPIntegration 測試 Gemini 與 MCP 的集成
func TestGemini_MCPIntegration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建 Gemini 實例
		geminiLLM := gemini.New()
		t.AssertNE(geminiLLM, nil)

		// 創建測試配置（MCP 配置現在直接從配置文件讀取）
		config := &llm.LLMsConfig{
			// MCP 配置已移除，現在由 ConfigManager 直接從配置文件讀取
		}

		// 創建測試 payload
		payload := &llm.Payload{
			SystemInstruction: "You are a helpful assistant.",
		}

		// 測試初始化
		err := geminiLLM.Initialize(ctx, config, payload)
		if err != nil {
			t.Logf("Initialize failed as expected (no valid Gemini config): %v", err)
		}

		// 清理
		geminiLLM.Release(ctx)
	})
}

// TestMCPConfig_Validation 測試 MCP 配置驗證
func TestMCPConfig_Validation(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試 ConfigManager 的配置載入與驗證（若無配置，LoadMCPConfig 可能回傳 nil）
		ctx := context.Background()
		configManager := mcp.NewConfigManager()
		config, err := configManager.LoadMCPConfig(ctx)
		t.AssertNil(err)
		// 無論 config 是否為 nil，ValidateConfig 都應無 panic；nil/disabled 視為有效
		err = configManager.ValidateConfig(config)
		if config != nil && config.Enabled && len(config.Servers) == 0 {
			t.AssertNE(err, nil)
		} else {
			t.AssertNil(err)
		}
	})
}

// TestMCPToolManager_Integration 測試 MCP 工具管理器集成
func TestMCPToolManager_Integration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 測試配置：禁用以避免實際連接
		config := &mcp.MCPConfig{Enabled: false}

		// 創建工具管理器
		manager := mcp.NewMCPToolManager(config, &testLogger{})
		t.AssertNE(manager, nil)

		// 測試初始化
		err := manager.Initialize(ctx)
		t.AssertNil(err)

		// 測試獲取工具定義
		tools, err := manager.GetToolDefinitions(ctx)
		t.AssertNil(err)
		t.AssertEQ(len(tools), 0)

		// 測試連線狀態
		status := manager.GetConnectionStatus()
		t.AssertEQ(len(status), 0)

		// 清理
		err = manager.Close(ctx)
		t.AssertNil(err)
	})
}

// TestLLMRouter_MCPIntegration 測試 LLM Router 與 MCP 的集成
func TestLLMRouter_MCPIntegration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 這個測試需要實際的 LLM Router 實例
		// 由於依賴複雜，這裡只做基本的配置測試

		// 測試 MCP 配置結構（僅結構驗證）
		mcpConfig := &mcp.MCPConfig{
			Enabled: true,
			Servers: []mcp.MCPServerConfig{
				{Name: "test-stdio", Type: "stdio", Command: "bash", Args: []string{"-c", "true"}, Timeout: 5},
				{Name: "test-stream", Type: "streamable", URL: "http://localhost:8080/mcp", Timeout: 30},
			},
			Global: mcp.MCPGlobalConfig{},
		}

		// 驗證配置結構
		t.AssertEQ(mcpConfig.Enabled, true)
		t.AssertEQ(len(mcpConfig.Servers), 2)

		// 驗證 STDIO 服務器配置
		stdioServer := mcpConfig.Servers[0]
		t.AssertEQ(stdioServer.Name, "test-stdio")
		t.AssertEQ(stdioServer.Type, "stdio")
		t.AssertEQ(stdioServer.Command, "bash")
		t.AssertEQ(len(stdioServer.Args), 2)

		// 驗證 streamable 服務器配置
		streamServer := mcpConfig.Servers[1]
		t.AssertEQ(streamServer.Name, "test-stream")
		t.AssertEQ(streamServer.Type, "streamable")
		t.AssertEQ(streamServer.URL, "http://localhost:8080/mcp")
	})
}

// TestLLM_MCPErrorHandling 測試 LLM 中的 MCP 錯誤處理
func TestLLM_MCPErrorHandling(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 無效的服務器配置（不支持的類型）
		invalidConfig := &mcp.MCPConfig{
			Enabled: true,
			Servers: []mcp.MCPServerConfig{{Name: "invalid-server", Type: "invalid-type", Timeout: 30}},
			Global:  mcp.MCPGlobalConfig{},
		}

		manager := mcp.NewMCPToolManager(invalidConfig, &testLogger{})
		t.AssertNE(manager, nil)

		err := manager.Initialize(ctx)
		if err != nil {
			t.Logf("Initialize failed as expected for invalid config: %v", err)
		}

		t.AssertNil(manager.Close(ctx))
	})
}

// BenchmarkMCPIntegration 性能測試
func BenchmarkMCPIntegration(b *testing.B) {
	ctx := context.Background()

	config := &mcp.MCPConfig{Enabled: false}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		manager := mcp.NewMCPToolManager(config, &testLogger{})
		_ = manager.Initialize(ctx)
		_, _ = manager.GetToolDefinitions(ctx)
		_ = manager.Close(ctx)
	}
}
