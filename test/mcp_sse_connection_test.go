package test

import (
	"context"
	"testing"
	"time"

	"brainHub/internal/llms/mcp"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestMCPSSEConnection_WithProvidedConfig 使用用戶提供的配置測試 SSE 連接
func TestMCPSSEConnection_WithProvidedConfig(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 使用用戶提供的配置值
		config := &mcp.UnifiedMCPConfig{
			Enabled: true,
			Clients: []*mcp.MCPClientConfig{
				{
					Name:      "faceRecognition",
					Type:      "sse", // 使用 SSE 類型
					ServerURL: "https://csai_uat_deepface.chainsea.com.tw/mcp/",
					Headers: map[string]string{
						"CS-API-Key": "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU",
					},
					Timeout:     60 * time.Second,
					MaxRetries:  3,
					AutoApprove: []string{},
					Disabled:    false,
				},
			},
			GlobalConfig: &mcp.MCPGlobalConfig{
				Timeout:     60,
				Environment: make(map[string]string),
			},
		}

		// 創建統一 MCP 管理器
		manager := mcp.NewUnifiedMCPManager(config)
		t.AssertNE(manager, nil)

		// 測試初始化
		t.Logf("Testing MCP SSE connection with provided config...")
		err := manager.Initialize(ctx)
		if err != nil {
			t.Logf("Initialize failed: %v", err)
		} else {
			t.Logf("Initialize succeeded!")
		}

		// 測試連接狀態
		connectionStatus := manager.GetConnectionStatus()
		t.Logf("Connection status: %+v", connectionStatus)

		// 測試工具定義獲取
		if len(connectionStatus) > 0 {
			for clientName, connected := range connectionStatus {
				t.Logf("Client '%s' connected: %v", clientName, connected)

				if connected {
					// 嘗試獲取工具定義
					tools, err := manager.GetToolDefinitions(ctx)
					if err != nil {
						t.Logf("Failed to get tool definitions: %v", err)
					} else {
						t.Logf("Successfully retrieved %d tool definitions", len(tools))
						for i, tool := range tools {
							t.Logf("Tool %d: %s - %s", i+1, tool.Name, tool.Description)
						}
					}
				}
			}
		}

		// 清理
		err = manager.Close(ctx)
		if err != nil {
			t.Logf("Failed to close manager: %v", err)
		} else {
			t.Logf("Manager closed successfully")
		}
	})
}

// TestMCPSSEConnection_DirectClient 直接測試 SSE 客戶端
func TestMCPSSEConnection_DirectClient(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 創建客戶端配置
		clientConfig := &mcp.MCPClientConfig{
			Name:      "faceRecognition",
			Type:      "sse",
			ServerURL: "https://csai_uat_deepface.chainsea.com.tw/mcp/",
			Headers: map[string]string{
				"CS-API-Key": "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU",
			},
			Timeout:     60 * time.Second,
			MaxRetries:  3,
			AutoApprove: []string{},
			Disabled:    false,
		}

		// 創建統一 MCP 客戶端
		client := mcp.NewUnifiedMCPClient(clientConfig)
		t.AssertNE(client, nil)

		t.Logf("Testing direct SSE client connection...")
		t.Logf("Server URL: %s", clientConfig.ServerURL)
		t.Logf("Headers: %+v", clientConfig.Headers)
		t.Logf("Type: %s", clientConfig.Type)

		// 測試初始化
		err := client.Initialize(ctx)
		if err != nil {
			t.Logf("Client initialize failed: %v", err)
		} else {
			t.Logf("Client initialize succeeded!")

			// 測試連接狀態
			connected := client.IsConnected()
			t.Logf("Client connected: %v", connected)

			if connected {
				// 嘗試獲取工具定義
				tools, err := client.GetToolDefinitions(ctx)
				if err != nil {
					t.Logf("Failed to get tool definitions: %v", err)
				} else {
					t.Logf("Successfully retrieved %d tool definitions", len(tools))
					for i, tool := range tools {
						t.Logf("Tool %d: %s - %s", i+1, tool.Name, tool.Description)
					}
				}
			}
		}

		// 清理
		err = client.Close()
		if err != nil {
			t.Logf("Failed to close client: %v", err)
		} else {
			t.Logf("Client closed successfully")
		}
	})
}

// TestMCPSSEConnection_TransportCreation 測試 SSE 傳輸創建
func TestMCPSSEConnection_TransportCreation(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {

		// 創建客戶端配置
		clientConfig := &mcp.MCPClientConfig{
			Name:      "faceRecognition",
			Type:      "sse",
			ServerURL: "https://csai_uat_deepface.chainsea.com.tw/mcp/",
			Headers: map[string]string{
				"CS-API-Key": "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU",
			},
			Timeout:     60 * time.Second,
			MaxRetries:  3,
			AutoApprove: []string{},
			Disabled:    false,
		}

		// 創建統一 MCP 客戶端
		client := mcp.NewUnifiedMCPClient(clientConfig)
		t.AssertNE(client, nil)

		t.Logf("Testing SSE transport creation...")
		t.Logf("Config: %+v", clientConfig)

		// 測試配置驗證
		config := client.GetConfig()
		t.AssertEQ(config.Type, "sse")
		t.AssertEQ(config.ServerURL, "https://csai_uat_deepface.chainsea.com.tw/mcp/")
		t.AssertEQ(config.Headers["CS-API-Key"], "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU")

		t.Logf("Configuration validation passed")
		t.Logf("Type: %s", config.Type)
		t.Logf("URL: %s", config.ServerURL)
		t.Logf("Headers: %+v", config.Headers)
		t.Logf("Timeout: %v", config.Timeout)
	})
}

// TestMCPSSEConnection_CompareWithStreamable 比較 SSE 和 Streamable 的差異
func TestMCPSSEConnection_CompareWithStreamable(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 測試 SSE 配置
		sseConfig := &mcp.MCPClientConfig{
			Name:      "faceRecognition-sse",
			Type:      "sse",
			ServerURL: "https://csai_uat_deepface.chainsea.com.tw/mcp/",
			Headers: map[string]string{
				"CS-API-Key": "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU",
			},
			Timeout:     60 * time.Second,
			MaxRetries:  3,
			AutoApprove: []string{},
			Disabled:    false,
		}

		// 測試 Streamable 配置
		streamableConfig := &mcp.MCPClientConfig{
			Name:      "faceRecognition-streamable",
			Type:      "streamable",
			ServerURL: "https://csai_uat_deepface.chainsea.com.tw/mcp/",
			Headers: map[string]string{
				"CS-API-Key": "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU",
			},
			Timeout:     60 * time.Second,
			MaxRetries:  3,
			AutoApprove: []string{},
			Disabled:    false,
		}

		t.Logf("Comparing SSE vs Streamable transport types...")

		// 測試 SSE 客戶端
		t.Logf("=== Testing SSE Client ===")
		sseClient := mcp.NewUnifiedMCPClient(sseConfig)
		err := sseClient.Initialize(ctx)
		if err != nil {
			t.Logf("SSE client failed: %v", err)
		} else {
			t.Logf("SSE client succeeded!")
		}
		sseClient.Close()

		// 測試 Streamable 客戶端
		t.Logf("=== Testing Streamable Client ===")
		streamableClient := mcp.NewUnifiedMCPClient(streamableConfig)
		err = streamableClient.Initialize(ctx)
		if err != nil {
			t.Logf("Streamable client failed: %v", err)
		} else {
			t.Logf("Streamable client succeeded!")
		}
		streamableClient.Close()

		t.Logf("Comparison completed")
	})
}
