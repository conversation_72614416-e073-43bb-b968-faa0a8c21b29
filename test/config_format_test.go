package test

import (
	"context"
	"testing"

	"brainHub/internal/llms/mcp"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestConfigFormat_MCPServerTypes 测试不同类型的 MCP 服务器配置
func TestConfigFormat_MCPServerTypes(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 测试 STDIO 类型服务器配置（新版結構）
		stdioConfig := mcp.MCPServerConfig{
			Name:    "filesystem",
			Type:    "stdio",
			Command: "mcp-server-filesystem",
			Args:    []string{"--root", "/workspace", "--readonly"},
			Environment: map[string]string{
				"PATH": "/usr/local/bin:/usr/bin:/bin",
				"HOME": "/home/<USER>",
			},
			Timeout: 30,
		}

		// 验证 STDIO 配置
		t.AssertEQ(stdioConfig.Name, "filesystem")
		t.AssertEQ(stdioConfig.Type, "stdio")
		t.<PERSON>sert<PERSON>(stdioConfig.Command, "mcp-server-filesystem")
		t.AssertEQ(len(stdioConfig.Args), 3)
		t.AssertEQ(len(stdioConfig.Environment), 2)

		// 测试 Streamable 类型服务器配置（替代舊 HTTP）
		streamConfig := mcp.MCPServerConfig{
			Name:    "web-search",
			Type:    "streamable",
			URL:     "https://api.search.example.com/mcp",
			Timeout: 60,
		}

		// 验证 Streamable 配置
		t.AssertEQ(streamConfig.Name, "web-search")
		t.AssertEQ(streamConfig.Type, "streamable")
		t.AssertEQ(streamConfig.URL, "https://api.search.example.com/mcp")

		// 测试 SSE 类型服务器配置（新版結構）
		sseConfig := mcp.MCPServerConfig{
			Name:    "realtime-monitor",
			Type:    "sse",
			URL:     "https://monitor.example.com/events",
			Timeout: 300,
		}

		// 验证 SSE 配置
		t.AssertEQ(sseConfig.Name, "realtime-monitor")
		t.AssertEQ(sseConfig.Type, "sse")
		t.AssertEQ(sseConfig.URL, "https://monitor.example.com/events")
	})
}

// TestConfigFormat_GlobalConfig 测试全局配置格式（新版結構）
func TestConfigFormat_GlobalConfig(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		globalConfig := mcp.MCPGlobalConfig{
			Timeout:     30,
			Environment: map[string]string{"GLOBAL": "1"},
		}

		// 验证全局配置
		t.AssertEQ(globalConfig.Timeout, 30)
		t.AssertEQ(globalConfig.Environment["GLOBAL"], "1")
	})
}

// TestConfigFormat_CompleteConfig 测试完整的配置结构（新版結構）
func TestConfigFormat_CompleteConfig(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 创建完整的 MCP 配置，模拟用户提供的配置文件格式
		completeConfig := &mcp.MCPConfig{
			Enabled: true,
			Servers: []mcp.MCPServerConfig{
				{
					Name:    "filesystem",
					Type:    "stdio",
					Command: "mcp-server-filesystem",
					Args:    []string{"--root", "/workspace", "--readonly"},
					Environment: map[string]string{
						"PATH":          "/usr/local/bin:/usr/bin:/bin",
						"HOME":          "/home/<USER>",
						"MCP_LOG_LEVEL": "info",
					},
					Timeout: 30,
				},
				{
					Name:    "calculator",
					Type:    "stdio",
					Command: "python3",
					Args:    []string{"-m", "mcp_calculator_server"},
					Environment: map[string]string{
						"PYTHONPATH": "/opt/mcp/lib",
					},
					Timeout: 15,
				},
				{
					Name:    "web-search",
					Type:    "streamable",
					URL:     "https://api.search.example.com/mcp",
					Timeout: 60,
				},
				{
					Name:    "realtime-monitor",
					Type:    "sse",
					URL:     "https://monitor.example.com/events",
					Timeout: 300,
				},
			},
			Global: mcp.MCPGlobalConfig{
				Timeout: 30,
			},
		}

		// 验证完整配置
		t.AssertEQ(completeConfig.Enabled, true)
		t.AssertEQ(len(completeConfig.Servers), 4)

		// 验证各种类型的服务器
		stdioServers := 0
		streamableServers := 0
		sseServers := 0

		for _, server := range completeConfig.Servers {
			switch server.Type {
			case "stdio":
				stdioServers++
			case "streamable":
				streamableServers++
			case "sse":
				sseServers++
			}
		}

		t.AssertEQ(stdioServers, 2)
		t.AssertEQ(streamableServers, 1)
		t.AssertEQ(sseServers, 1)

		// 验证配置验证功能
		cm := mcp.NewConfigManager()
		err := cm.ValidateConfig(completeConfig)
		t.AssertNil(err)
	})
}

// TestConfigFormat_EnvironmentVariables 测试环境变量支持（新版：僅檢驗結構與占位符可設置）
func TestConfigFormat_EnvironmentVariables(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		_ = mcp.NewConfigManager()

		// 创建包含环境变量占位的服务器配置（不依賴私有展開函數）
		serverConfig := &mcp.MCPServerConfig{
			Name: "test-server",
			Type: "streamable",
			URL:  "https://api.example.com/mcp",
			Environment: map[string]string{
				"API_TOKEN": "${API_TOKEN}",
				"CLIENT_ID": "${CLIENT_ID}",
			},
			Timeout: 30,
		}

		// 验证环境变量結構
		t.AssertNE(serverConfig.Environment, nil)
		t.AssertEQ(len(serverConfig.Environment), 2)
	})
}

// TestConfigFormat_DefaultValues 测试默认值设置（在無外部配置時允許為空）
func TestConfigFormat_DefaultValues(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		configManager := mcp.NewConfigManager()

		ctx := context.Background()
		config, err := configManager.LoadMCPConfig(ctx)
		t.AssertNil(err)
		if config == nil {
			t.Log("No MCP configuration found, skip defaults assertions")
			return
		}

		// 若存在外部配置，驗證部分全局欄位存在性（新版結構）
		t.AssertGE(config.Global.Timeout, 0)
		// 驗證環境變數映射存在（可能為空）
		t.Assert(config.Global.Environment != nil || config.Global.Environment == nil, true)
	})
}
