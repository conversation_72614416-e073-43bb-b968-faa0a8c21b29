# MCP (Model Context Protocol) 配置示例
# 本文件展示了如何在 brainHub 項目中配置 MCP 服務器
#
# 重要變更 (v2.0):
# - 配置結構已更新為 mcp_config 和 mcp_servers 分離
# - 配置直接從配置文件讀取，不再通過 LLMsConfig 傳遞
# - 支持配置緩存和熱更新

# ==========================================
# MCP 全局配置（對應 mcp_config 路徑）
# ==========================================
mcp_config:
  # 是否啟用 MCP 功能
  enabled: true

  # 全局配置項
  default_timeout: "30s"
  max_concurrent_calls: 20
  cache_ttl: "10m"
  log_level: "info"

# ==========================================
# MCP 服務器配置列表（對應 mcp_servers 路徑）
# ==========================================
mcp_servers:
  # ------------------------------------------
  # STDIO 類型服務器示例 - 用於本地命令行工具
  # ------------------------------------------
  - name: "filesystem"
    type: "stdio"
    description: "文件系統操作工具"
    command: "mcp-server-filesystem"
    args:
      - "--root"
      - "/workspace"
      - "--readonly"
    env:
      - "PATH=/usr/local/bin:/usr/bin:/bin"
      - "HOME=/home/<USER>"
      - "MCP_LOG_LEVEL=info"
    timeout: "30s"
    retry_count: 3

  - name: "calculator"
    type: "stdio"
    description: "數學計算工具"
    command: "python3"
    args:
      - "-m"
      - "mcp_calculator_server"
    env:
      - "PYTHONPATH=/opt/mcp/lib"
    timeout: "15s"
    retry_count: 2

  # ------------------------------------------
  # HTTP 類型服務器示例 - 用於 Web 服務
  # ------------------------------------------
  - name: "web-search"
    type: "http"
    description: "網頁搜索服務"
    url: "https://api.search.example.com/mcp"
    headers:
      Authorization: "Bearer ${MCP_SEARCH_TOKEN}"
      Content-Type: "application/json"
      User-Agent: "brainHub-MCP/1.0"
      X-API-Version: "v1"
    timeout: "60s"
    retry_count: 3

  - name: "database-query"
    type: "http"
    description: "數據庫查詢服務"
    url: "http://localhost:8080/mcp/db"
    headers:
      Authorization: "Bearer ${DB_MCP_TOKEN}"
      Content-Type: "application/json"
      X-Database: "production"
    timeout: "45s"
    retry_count: 2

  # ------------------------------------------
  # SSE 類型服務器示例 - 用於實時應用（使用 Streamable HTTP 實現）
  # ------------------------------------------
  - name: "realtime-monitor"
    type: "sse"
    description: "實時監控數據流（Streamable HTTP MCP）"
    url: "https://monitor.example.com"  # 基礎 URL，端點會自動添加 (/initialize, /events, /call)
    headers:
      Authorization: "Bearer ${MONITOR_TOKEN}"
      MCP-Protocol-Version: "2024-11-05"
      X-Client-ID: "brainhub-streamable-client"
    timeout: "300s"
    retry_count: 3
    # Streamable HTTP 特定配置（可選）
    streamable_http:
      protocol_version: "2024-11-05"
      reconnect_interval: "5s"
      max_reconnect_attempts: 10
      request_timeout: "30s"
      event_buffer_size: 1000
      enable_metrics: true
      max_concurrent_streams: 5

---
# ==========================================
# 開發環境配置示例
# ==========================================
mcp_config:
  enabled: true
  default_timeout: "30s"
  max_concurrent_calls: 5
  cache_ttl: "1m"
  log_level: "debug"

mcp_servers:
  # 本地開發工具
  - name: "dev-tools"
    type: "stdio"
    description: "開發工具集"
    command: "python"
    args: ["-m", "mcp_dev_tools"]
    env:
      - "PYTHONPATH=/opt/dev/lib"
      - "MCP_ENV=development"
    timeout: "30s"
    retry_count: 3

  # 測試 API 服務
  - name: "test-api"
    type: "http"
    description: "測試 API 服務"
    url: "http://localhost:3000/mcp"
    headers:
      X-Environment: "development"
      Content-Type: "application/json"
    timeout: "30s"
    retry_count: 3

---
# ==========================================
# 生產環境配置示例
# ==========================================
mcp_config:
  enabled: true
  default_timeout: "60s"
  max_concurrent_calls: 20
  cache_ttl: "10m"
  log_level: "info"

mcp_servers:
  # 生產文件系統服務
  - name: "prod-filesystem"
    type: "stdio"
    description: "生產環境文件系統"
    command: "/opt/mcp/bin/filesystem-server"
    args:
      - "--root"
      - "/data"
      - "--readonly"
      - "--security-mode=strict"
    env:
      - "MCP_LOG_LEVEL=warn"
      - "MCP_SECURITY_MODE=strict"
      - "PATH=/opt/mcp/bin:/usr/bin:/bin"
    timeout: "60s"
    retry_count: 5

  # 外部 API 服務
  - name: "external-api"
    type: "http"
    description: "外部 API 服務"
    url: "https://api.example.com/mcp"
    headers:
      Authorization: "Bearer ${MCP_API_TOKEN}"
      User-Agent: "brainHub/1.0"
      X-Environment: "production"
      Content-Type: "application/json"
    timeout: "120s"
    retry_count: 3

  # 監控和日誌服務
  - name: "monitoring"
    type: "sse"
    description: "監控數據流"
    url: "https://monitoring.example.com/events"
    headers:
      Authorization: "Bearer ${MCP_MONITORING_TOKEN}"
      Accept: "text/event-stream"
      Cache-Control: "no-cache"
    timeout: "300s"
    retry_count: 1

---
# ==========================================
# 禁用 MCP 配置示例
# ==========================================
mcp_config:
  enabled: false
  default_timeout: "30s"
  max_concurrent_calls: 10
  cache_ttl: "5m"
  log_level: "info"

mcp_servers: []  # 空數組，不配置任何服務器

---
# 特定租戶配置示例（在租戶參數中）
tenant_specific_mcp:
  # 租戶 ID: tenant_001
  tenant_001:
    mcp_config:
      enabled: true
      servers:
        - name: "tenant-specific-tool"
          type: "stdio"
          description: "租戶專用工具"
          command: "tenant-tool"
          args: ["--tenant", "tenant_001"]
          timeout: "30s"
          retry_count: 3
      global:
        enabled: true
        default_timeout: "30s"
        max_concurrent_calls: 5
        cache_ttl: "2m"
        log_level: "info"
  
  # 租戶 ID: tenant_002
  tenant_002:
    mcp_config:
      enabled: true
      servers:
        - name: "enterprise-tools"
          type: "http"
          description: "企業級工具"
          url: "https://enterprise.example.com/mcp"
          headers:
            X-Tenant-ID: "tenant_002"
            Authorization: "Bearer ${ENTERPRISE_TOKEN}"
          timeout: "60s"
          retry_count: 3
      global:
        enabled: true
        default_timeout: "60s"
        max_concurrent_calls: 15
        cache_ttl: "15m"
        log_level: "info"

---
# 安全配置示例
mcp_security:
  # 環境變量配置
  environment_variables:
    - MCP_API_TOKEN: "your-secure-api-token"
    - MCP_MONITORING_TOKEN: "your-monitoring-token"
    - ENTERPRISE_TOKEN: "your-enterprise-token"
  
  # 網絡安全配置
  network_security:
    allowed_hosts:
      - "localhost"
      - "127.0.0.1"
      - "api.example.com"
      - "monitoring.example.com"
    
    ssl_verification: true
    timeout_limits:
      min: "1s"
      max: "300s"
  
  # 資源限制
  resource_limits:
    max_memory_per_client: "100MB"
    max_cpu_per_client: "50%"
    max_file_size: "10MB"
    max_concurrent_requests: 50

---
# 監控和日誌配置示例
mcp_monitoring:
  # 日誌配置
  logging:
    level: "info"  # debug, info, warn, error
    format: "json"  # json, text
    output: "stdout"  # stdout, file, syslog
    file_path: "/var/log/brainhub/mcp.log"
    max_size: "100MB"
    max_backups: 5
    max_age: "30d"
  
  # 指標配置
  metrics:
    enabled: true
    endpoint: "/metrics"
    port: 9090
    
    # 自定義指標
    custom_metrics:
      - name: "mcp_tool_calls_total"
        type: "counter"
        description: "Total number of MCP tool calls"
      
      - name: "mcp_tool_call_duration"
        type: "histogram"
        description: "Duration of MCP tool calls"
      
      - name: "mcp_active_connections"
        type: "gauge"
        description: "Number of active MCP connections"
  
  # 健康檢查配置
  health_check:
    enabled: true
    endpoint: "/health"
    interval: "30s"
    timeout: "10s"
    
    # 檢查項目
    checks:
      - name: "mcp_clients"
        description: "Check MCP client connections"
      
      - name: "tool_availability"
        description: "Check tool availability"
      
      - name: "resource_usage"
        description: "Check resource usage"
