# MCP API 參考文檔

## 概述

本文檔提供了 brainHub 項目中 MCP (Model Context Protocol) 相關 API 的詳細參考信息，包括接口定義、參數說明、響應格式和使用示例。

## API 基礎信息

### 基礎 URL
```
開發環境: http://localhost:8000/api/v1
生產環境: https://api.brainhub.com/api/v1
```

### 認證方式
```http
Authorization: Bearer <your-api-token>
Content-Type: application/json
```

### 響應格式
所有 API 響應都遵循統一的格式：

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "req_123456789"
}
```

## MCP 管理 API

### 1. 獲取 MCP 狀態

#### 請求
```http
GET /mcp/status
```

#### 響應
```json
{
  "success": true,
  "data": {
    "enabled": true,
    "total_servers": 3,
    "active_servers": 2,
    "total_tools": 15,
    "health_status": "healthy",
    "last_update": "2024-01-01T00:00:00Z"
  },
  "message": "MCP status retrieved successfully"
}
```

### 2. 獲取服務器列表

#### 請求
```http
GET /mcp/servers
```

#### 查詢參數
- `type` (可選): 服務器類型 (stdio, http, sse)
  - `stdio`: 標準輸入輸出客戶端
  - `http`: HTTP 請求客戶端
  - `sse`: Streamable HTTP 客戶端（符合 MCP 規範）
- `status` (可選): 服務器狀態 (active, inactive, error)
- `page` (可選): 頁碼，默認 1
- `limit` (可選): 每頁數量，默認 20

#### 響應
```json
{
  "success": true,
  "data": {
    "servers": [
      {
        "name": "filesystem",
        "type": "stdio",
        "description": "文件系統操作工具",
        "status": "active",
        "last_ping": "2024-01-01T00:00:00Z",
        "tools_count": 5,
        "config": {
          "command": "mcp-server-filesystem",
          "args": ["--root", "/workspace"],
          "timeout": "30s",
          "retry_count": 3
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 3,
      "total_pages": 1
    }
  },
  "message": "Servers retrieved successfully"
}
```

### 3. 獲取工具列表

#### 請求
```http
GET /mcp/tools
```

#### 查詢參數
- `server` (可選): 服務器名稱
- `category` (可選): 工具分類
- `search` (可選): 搜索關鍵詞
- `page` (可選): 頁碼，默認 1
- `limit` (可選): 每頁數量，默認 50

#### 響應
```json
{
  "success": true,
  "data": {
    "tools": [
      {
        "name": "filesystem.read_file",
        "description": "讀取文件內容",
        "server": "filesystem",
        "category": "file_operations",
        "parameters": {
          "type": "object",
          "properties": {
            "path": {
              "type": "string",
              "description": "文件路徑"
            }
          },
          "required": ["path"]
        },
        "examples": [
          {
            "description": "讀取配置文件",
            "parameters": {
              "path": "/config/app.yaml"
            }
          }
        ]
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 15,
      "total_pages": 1
    }
  },
  "message": "Tools retrieved successfully"
}
```

### 4. 調用工具

#### 請求
```http
POST /mcp/tools/call
```

#### 請求體
```json
{
  "tool_name": "filesystem.read_file",
  "parameters": {
    "path": "/workspace/example.txt"
  },
  "timeout": "30s"
}
```

#### 響應
```json
{
  "success": true,
  "data": {
    "tool_name": "filesystem.read_file",
    "result": {
      "success": true,
      "content": "文件內容...",
      "metadata": {
        "file_size": 1024,
        "last_modified": "2024-01-01T00:00:00Z"
      }
    },
    "execution_time": "150ms",
    "server": "filesystem"
  },
  "message": "Tool executed successfully"
}
```

### 5. 批量調用工具

#### 請求
```http
POST /mcp/tools/batch
```

#### 請求體
```json
{
  "calls": [
    {
      "id": "call_1",
      "tool_name": "filesystem.read_file",
      "parameters": {
        "path": "/workspace/file1.txt"
      }
    },
    {
      "id": "call_2",
      "tool_name": "filesystem.list_directory",
      "parameters": {
        "path": "/workspace"
      }
    }
  ],
  "timeout": "60s",
  "parallel": true
}
```

#### 響應
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": "call_1",
        "success": true,
        "result": {
          "content": "文件1內容..."
        },
        "execution_time": "120ms"
      },
      {
        "id": "call_2",
        "success": true,
        "result": {
          "files": ["file1.txt", "file2.txt"]
        },
        "execution_time": "80ms"
      }
    ],
    "total_execution_time": "200ms"
  },
  "message": "Batch execution completed"
}
```

## 配置管理 API

### 1. 獲取 MCP 配置

#### 請求
```http
GET /mcp/config
```

#### 響應
```json
{
  "success": true,
  "data": {
    "enabled": true,
    "servers": [
      {
        "name": "filesystem",
        "type": "stdio",
        "description": "文件系統操作工具",
        "command": "mcp-server-filesystem",
        "args": ["--root", "/workspace", "--readonly"],
        "env": ["PATH=/usr/local/bin:/usr/bin:/bin", "MCP_LOG_LEVEL=info"],
        "timeout": "30s",
        "retry_count": 3
      },
      {
        "name": "web-search",
        "type": "http",
        "description": "網頁搜索服務",
        "url": "https://api.search.example.com/mcp",
        "headers": {
          "Authorization": "Bearer ${MCP_SEARCH_TOKEN}",
          "Content-Type": "application/json"
        },
        "timeout": "60s",
        "retry_count": 3
      }
    ],
    "global": {
      "enabled": true,
      "default_timeout": "30s",
      "max_concurrent_calls": 20,
      "cache_ttl": "10m",
      "log_level": "info"
    },
    "cache_info": {
      "cache_ttl": "5m0s",
      "has_cache": true,
      "cache_age": "2m30s",
      "cache_valid": true
    }
  },
  "message": "Configuration retrieved successfully"
}
```

### 2. 更新 MCP 配置

#### 請求
```http
PUT /mcp/config
```

#### 請求體
```json
{
  "mcp_config": {
    "enabled": true,
    "default_timeout": "45s",
    "max_concurrent_calls": 15,
    "cache_ttl": "10m",
    "log_level": "info"
  },
  "mcp_servers": [
    {
      "name": "new-server",
      "type": "http",
      "description": "新的 HTTP 服務器",
      "url": "https://api.example.com/mcp",
      "headers": {
        "Authorization": "Bearer ${API_TOKEN}",
        "Content-Type": "application/json",
        "User-Agent": "brainHub-MCP/1.0"
      },
      "timeout": "60s",
      "retry_count": 2
    },
    {
      "name": "calculator",
      "type": "stdio",
      "description": "數學計算工具",
      "command": "python3",
      "args": ["-m", "mcp_calculator_server"],
      "env": ["PYTHONPATH=/opt/mcp/lib"],
      "timeout": "15s",
      "retry_count": 2
    }
  ]
}
```

#### 響應
```json
{
  "success": true,
  "data": {
    "updated": true,
    "restart_required": true,
    "affected_servers": ["new-server"]
  },
  "message": "Configuration updated successfully"
}
```

### 3. 重載配置

#### 請求
```http
POST /mcp/config/reload
```

#### 響應
```json
{
  "success": true,
  "data": {
    "reloaded": true,
    "cache_cleared": true,
    "servers_restarted": 2,
    "tools_refreshed": 15,
    "config_source": "file"
  },
  "message": "Configuration reloaded successfully"
}
```

### 4. 清理配置緩存

#### 請求
```http
DELETE /mcp/config/cache
```

#### 響應
```json
{
  "success": true,
  "data": {
    "cache_cleared": true,
    "previous_cache_age": "4m30s",
    "next_reload_from": "file"
  },
  "message": "Configuration cache cleared successfully"
}
```

### 5. 獲取配置緩存信息

#### 請求
```http
GET /mcp/config/cache
```

#### 響應
```json
{
  "success": true,
  "data": {
    "cache_ttl": "5m0s",
    "has_cache": true,
    "cache_age": "2m15s",
    "cache_valid": true,
    "servers_count": 3,
    "enabled": true,
    "last_updated": "2024-01-01T00:00:00Z"
  },
  "message": "Cache information retrieved successfully"
}
```

## 監控和統計 API

### 1. 獲取統計信息

#### 請求
```http
GET /mcp/stats
```

#### 查詢參數
- `period` (可選): 統計周期 (1h, 24h, 7d, 30d)，默認 24h
- `server` (可選): 特定服務器名稱

#### 響應
```json
{
  "success": true,
  "data": {
    "period": "24h",
    "total_calls": 1250,
    "successful_calls": 1180,
    "failed_calls": 70,
    "success_rate": 94.4,
    "average_response_time": "245ms",
    "cache_hit_rate": 78.5,
    "servers": {
      "filesystem": {
        "calls": 800,
        "success_rate": 96.2,
        "avg_response_time": "120ms"
      },
      "web-api": {
        "calls": 450,
        "success_rate": 91.1,
        "avg_response_time": "450ms"
      }
    },
    "top_tools": [
      {
        "name": "filesystem.read_file",
        "calls": 320,
        "success_rate": 98.1
      },
      {
        "name": "web-api.search",
        "calls": 280,
        "success_rate": 89.3
      }
    ]
  },
  "message": "Statistics retrieved successfully"
}
```

### 2. 獲取健康檢查

#### 請求
```http
GET /mcp/health
```

#### 響應
```json
{
  "success": true,
  "data": {
    "overall_status": "healthy",
    "servers": {
      "filesystem": {
        "status": "healthy",
        "last_check": "2024-01-01T00:00:00Z",
        "response_time": "50ms"
      },
      "web-api": {
        "status": "degraded",
        "last_check": "2024-01-01T00:00:00Z",
        "response_time": "800ms",
        "issues": ["high_latency"]
      }
    },
    "system": {
      "memory_usage": "45%",
      "cpu_usage": "23%",
      "active_connections": 15
    }
  },
  "message": "Health check completed"
}
```

### 3. 獲取日誌

#### 請求
```http
GET /mcp/logs
```

#### 查詢參數
- `level` (可選): 日誌級別 (debug, info, warn, error)
- `server` (可選): 服務器名稱
- `start_time` (可選): 開始時間 (ISO 8601)
- `end_time` (可選): 結束時間 (ISO 8601)
- `limit` (可選): 返回條數，默認 100

#### 響應
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "timestamp": "2024-01-01T00:00:00Z",
        "level": "info",
        "server": "filesystem",
        "message": "Tool call completed successfully",
        "metadata": {
          "tool": "read_file",
          "duration": "120ms",
          "request_id": "req_123"
        }
      }
    ],
    "total": 1500,
    "has_more": true
  },
  "message": "Logs retrieved successfully"
}
```

## WebSocket API

### 1. 實時事件訂閱

#### 連接
```javascript
const ws = new WebSocket('ws://localhost:8000/api/v1/mcp/events');
```

#### 訂閱事件
```json
{
  "action": "subscribe",
  "events": ["tool_call", "server_status", "error"],
  "filters": {
    "server": "filesystem"
  }
}
```

#### 事件格式
```json
{
  "event": "tool_call",
  "timestamp": "2024-01-01T00:00:00Z",
  "data": {
    "tool_name": "filesystem.read_file",
    "server": "filesystem",
    "status": "completed",
    "duration": "120ms",
    "request_id": "req_123"
  }
}
```

## 錯誤處理

### 錯誤響應格式
```json
{
  "success": false,
  "error": {
    "code": "TOOL_NOT_FOUND",
    "message": "指定的工具不存在",
    "details": {
      "tool_name": "invalid.tool",
      "available_tools": ["filesystem.read_file", "filesystem.write_file"]
    }
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "req_123456789"
}
```

### 常見錯誤碼

| 錯誤碼 | HTTP 狀態碼 | 描述 |
|--------|-------------|------|
| `INVALID_REQUEST` | 400 | 請求參數無效 |
| `UNAUTHORIZED` | 401 | 未授權訪問 |
| `FORBIDDEN` | 403 | 權限不足 |
| `TOOL_NOT_FOUND` | 404 | 工具不存在 |
| `SERVER_NOT_FOUND` | 404 | 服務器不存在 |
| `TOOL_EXECUTION_FAILED` | 422 | 工具執行失敗 |
| `SERVER_UNAVAILABLE` | 503 | 服務器不可用 |
| `TIMEOUT` | 504 | 請求超時 |
| `INTERNAL_ERROR` | 500 | 內部服務器錯誤 |

## SDK 和客戶端庫

### Go SDK 示例
```go
package main

import (
    "context"
    "fmt"
    "github.com/brainhub/mcp-go-sdk"
)

func main() {
    client := mcpsdk.NewClient("http://localhost:8000/api/v1", "your-api-token")
    
    // 調用工具
    result, err := client.CallTool(context.Background(), &mcpsdk.ToolCallRequest{
        ToolName: "filesystem.read_file",
        Parameters: map[string]interface{}{
            "path": "/workspace/example.txt",
        },
    })
    
    if err != nil {
        panic(err)
    }
    
    fmt.Printf("Result: %s\n", result.Content)
}
```

### JavaScript SDK 示例
```javascript
import { MCPClient } from '@brainhub/mcp-js-sdk';

const client = new MCPClient({
  baseURL: 'http://localhost:8000/api/v1',
  apiToken: 'your-api-token'
});

// 調用工具
const result = await client.callTool({
  toolName: 'filesystem.read_file',
  parameters: {
    path: '/workspace/example.txt'
  }
});

console.log('Result:', result.content);
```

### Python SDK 示例
```python
from brainhub_mcp import MCPClient

client = MCPClient(
    base_url='http://localhost:8000/api/v1',
    api_token='your-api-token'
)

# 調用工具
result = client.call_tool(
    tool_name='filesystem.read_file',
    parameters={'path': '/workspace/example.txt'}
)

print(f"Result: {result.content}")
```

## 限制和配額

### API 限制
- 請求頻率: 1000 requests/minute
- 並發連接: 100 connections
- 請求大小: 10MB max
- 響應大小: 50MB max

### 工具調用限制
- 單次調用超時: 300s max
- 批量調用數量: 50 calls max
- 並發工具調用: 20 calls max

### 緩存策略
- 工具定義緩存: 15 minutes
- 配置緩存: 5 minutes
- 統計數據緩存: 1 minute

## SSE 客戶端（Streamable HTTP）詳細說明

### 概述
SSE 客戶端是 brainHub 中最先進的 MCP 客戶端實現，完全符合 MCP Streamable HTTP 規範。它提供了實時雙向通信能力，支持事件流、工具調用和會話管理。

### 特性
- **協議合規性**: 完全符合 MCP Streamable HTTP 規範
- **JSON-RPC 2.0**: 支持完整的 JSON-RPC 2.0 協議
- **會話管理**: 支持會話 ID 和協議版本協商
- **多流支持**: 支持多個並發 SSE 流連接
- **自動重連**: 支持斷線重連和事件重放（Last-Event-ID）
- **事件處理**: 支持標準 SSE 事件格式（id、event、data、retry）
- **監控指標**: 內置詳細的連接和性能監控
- **錯誤處理**: 完善的錯誤類型定義和恢復機制

### 配置示例
```yaml
mcp_servers:
  - name: "realtime-monitor"
    type: "sse"
    description: "實時監控數據流（Streamable HTTP MCP）"
    url: "https://monitor.example.com"  # 基礎 URL
    headers:
      Authorization: "Bearer ${MONITOR_TOKEN}"
      MCP-Protocol-Version: "2024-11-05"
      X-Client-ID: "brainhub-streamable-client"
    timeout: "300s"
    retry_count: 3
    # Streamable HTTP 特定配置
    streamable_http:
      protocol_version: "2024-11-05"
      reconnect_interval: "5s"
      max_reconnect_attempts: 10
      request_timeout: "30s"
      event_buffer_size: 1000
      enable_metrics: true
      max_concurrent_streams: 5
```

### 端點說明
SSE 客戶端會自動使用以下端點：
- `POST /initialize`: 協議初始化
- `GET /events`: SSE 事件流
- `POST /call`: 工具調用和通知

### 監控指標
SSE 客戶端提供詳細的監控指標：

```json
{
  "basic_metrics": {
    "connection_count": 1,
    "reconnection_count": 0,
    "events_received": 150,
    "events_processed": 150,
    "requests_sent": 25,
    "responses_received": 25,
    "error_count": 0
  },
  "performance_metrics": {
    "average_response_time": "250ms",
    "last_event_time": "2024-01-01T12:00:00Z",
    "time_since_last_event": "5s"
  },
  "stream_metrics": {
    "total_streams": 1,
    "active_streams": 1,
    "stream_details": {
      "stream-123": {
        "id": "stream-123",
        "url": "https://monitor.example.com/events",
        "connected": true,
        "last_event_id": "event-456",
        "reconnect_count": 0
      }
    }
  },
  "health_status": "healthy"
}
```

### 錯誤處理
SSE 客戶端定義了詳細的錯誤類型：

#### 連接錯誤
- `CONNECTION_FAILED`: 連接失敗
- `CONNECTION_TIMEOUT`: 連接超時
- `CONNECTION_LOST`: 連接丟失
- `RECONNECT_FAILED`: 重連失敗

#### 協議錯誤
- `PROTOCOL_MISMATCH`: 協議版本不匹配
- `INITIALIZE_FAILED`: 初始化失敗
- `SESSION_INVALID`: 會話無效

#### 流錯誤
- `STREAM_NOT_FOUND`: 流未找到
- `STREAM_CLOSED`: 流已關閉
- `STREAM_OVERFLOW`: 流緩衝區溢出
- `EVENT_PARSE_FAILED`: 事件解析失敗

### 最佳實踐
1. **配置合理的重連參數**: 設置適當的重連間隔和最大重連次數
2. **監控連接健康**: 定期檢查監控指標和健康狀態
3. **處理事件緩衝**: 根據事件頻率調整緩衝區大小
4. **錯誤恢復**: 實現適當的錯誤處理和恢復邏輯
5. **會話管理**: 正確處理會話 ID 和協議版本

通過本 API 參考文檔，開發者可以有效地集成和使用 brainHub 的 MCP 功能。
