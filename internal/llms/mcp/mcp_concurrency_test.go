package mcp_test

import (
    "context"
    "net/http"
    "net/http/httptest"
    "sync"
    "testing"

    bhmcp "brainHub/internal/llms/mcp"
    mcpgo "github.com/modelcontextprotocol/go-sdk/mcp"
)

// 重用 mcp_e2e_http_test.go 中的 buildTestServer 與 testLogger 類型

func TestMCP_ConcurrencyAndCleanup(t *testing.T) {
    ctx := context.Background()

    mux := http.NewServeMux()
    streamHandler := mcpgo.NewStreamableHTTPHandler(func(r *http.Request) *mcpgo.Server { return buildTestServer() }, nil)
    mux.Handle("/", streamHandler)

    ts := httptest.NewServer(mux)
    defer ts.Close()

    cfg := &bhmcp.MCPConfig{
        Enabled: true,
        Servers: []bhmcp.MCPServerConfig{{Name: "e2e-streamable", Type: bhmcp.TransportTypeStreamable, URL: ts.URL}},
    }

    mgr := bhmcp.NewMCPToolManager(cfg, &testLogger{t: t})
    if err := mgr.Initialize(ctx); err != nil {
        t.Fatalf("Initialize failed: %v", err)
    }

    // 多 goroutine 并发呼叫工具
    const workers = 5
    const callsPerWorker = 10

    var wg sync.WaitGroup
    wg.Add(workers)

    for w := 0; w < workers; w++ {
        w := w
        go func() {
            defer wg.Done()
            for i := 0; i < callsPerWorker; i++ {
                _, err := mgr.CallTool(ctx, "e2e-streamable.echo", map[string]interface{}{"text": "ping"})
                if err != nil {
                    t.Errorf("CallTool failed (w=%d i=%d): %v", w, i, err)
                    return
                }
            }
        }()
    }

    wg.Wait()

    // 關閉並驗證不報錯
    if err := mgr.Close(ctx); err != nil {
        t.Fatalf("Close failed: %v", err)
    }
}

