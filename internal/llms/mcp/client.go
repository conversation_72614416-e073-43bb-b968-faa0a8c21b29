package mcp

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/modelcontextprotocol/go-sdk/mcp"
)

// ClientInfo 客戶端信息
type ClientInfo struct {
	Name      string
	Client    *mcp.Client
	Session   *mcp.ClientSession
	Transport mcp.Transport
	Config    MCPServerConfig
	Connected bool
	LastError error
}

// MCPClientManager 新的客戶端管理器
type MCPClientManager struct {
	clients map[string]*ClientInfo
	config  *MCPConfig
	logger  Logger
	mutex   sync.RWMutex
}

// NewMCPClientManager 創建新的客戶端管理器
func NewMCPClientManager(config *MCPConfig, logger Logger) *MCPClientManager {
	return &MCPClientManager{
		clients: make(map[string]*ClientInfo),
		config:  config,
		logger:  logger,
	}
}

// Initialize 初始化所有客戶端
func (cm *MCPClientManager) Initialize(ctx context.Context) error {
	cm.logger.Debugf(ctx, "Initializing MCP client manager with %d servers", len(cm.config.Servers))

	var initErrors []error
	successCount := 0

	for _, serverConfig := range cm.config.Servers {
		if err := cm.initializeClient(ctx, serverConfig); err != nil {
			cm.logger.Errorf(ctx, "Failed to initialize client '%s': %v", serverConfig.Name, err)
			initErrors = append(initErrors, fmt.Errorf("server '%s': %w", serverConfig.Name, err))
			continue
		}
		successCount++
		cm.logger.Debugf(ctx, "Successfully initialized client '%s'", serverConfig.Name)
	}

	cm.logger.Debugf(ctx, "MCP client manager initialization completed: %d/%d servers successful", 
		successCount, len(cm.config.Servers))

	// 如果有部分失敗，記錄但不阻止整體初始化
	if len(initErrors) > 0 {
		cm.logger.Warningf(ctx, "Some MCP servers failed to initialize: %d errors", len(initErrors))
	}

	return nil
}

// initializeClient 初始化單個客戶端
func (cm *MCPClientManager) initializeClient(ctx context.Context, config MCPServerConfig) error {
	// 創建傳輸適配器
	adapter := NewTransportAdapter(config, cm.logger)
	
	// 驗證配置
	if err := adapter.ValidateConfig(); err != nil {
		return fmt.Errorf("invalid config: %w", err)
	}

	// 創建傳輸
	transport, err := adapter.CreateTransport(ctx)
	if err != nil {
		return fmt.Errorf("failed to create transport: %w", err)
	}

	// 創建官方SDK客戶端
	client := mcp.NewClient(&mcp.Implementation{
		Name:    "brainHub-mcp-client",
		Version: "v2.0.0",
	}, nil)

	// 建立連接（帶超時）
	connectCtx := ctx
	if config.Timeout > 0 {
		var cancel context.CancelFunc
		connectCtx, cancel = context.WithTimeout(ctx, time.Duration(config.Timeout)*time.Second)
		defer cancel()
	}

	session, err := client.Connect(connectCtx, transport, nil)
	if err != nil {
		return fmt.Errorf("failed to connect: %w", err)
	}

	// 存儲客戶端信息
	clientInfo := &ClientInfo{
		Name:      config.Name,
		Client:    client,
		Session:   session,
		Transport: transport,
		Config:    config,
		Connected: true,
	}

	cm.mutex.Lock()
	cm.clients[config.Name] = clientInfo
	cm.mutex.Unlock()

	cm.logger.Debugf(ctx, "Client '%s' connected successfully using %s transport", 
		config.Name, config.Type)

	return nil
}

// GetClient 獲取指定名稱的客戶端
func (cm *MCPClientManager) GetClient(name string) (*ClientInfo, bool) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	client, exists := cm.clients[name]
	return client, exists
}

// GetAllClients 獲取所有客戶端
func (cm *MCPClientManager) GetAllClients() map[string]*ClientInfo {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	result := make(map[string]*ClientInfo)
	for name, client := range cm.clients {
		result[name] = client
	}
	return result
}

// IsConnected 檢查指定客戶端是否已連接
func (cm *MCPClientManager) IsConnected(name string) bool {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	if client, exists := cm.clients[name]; exists {
		return client.Connected
	}
	return false
}

// Close 關閉所有客戶端連接
func (cm *MCPClientManager) Close(ctx context.Context) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	var closeErrors []error
	
	for name, client := range cm.clients {
		if client.Session != nil {
			if err := client.Session.Close(); err != nil {
				cm.logger.Errorf(ctx, "Failed to close session for client '%s': %v", name, err)
				closeErrors = append(closeErrors, fmt.Errorf("client '%s': %w", name, err))
			} else {
				cm.logger.Debugf(ctx, "Successfully closed session for client '%s'", name)
			}
		}
		client.Connected = false
	}

	if len(closeErrors) > 0 {
		return fmt.Errorf("errors closing clients: %v", closeErrors)
	}

	cm.logger.Debugf(ctx, "All MCP client sessions closed successfully")
	return nil
}

// GetConnectionStatus 獲取所有客戶端的連接狀態
func (cm *MCPClientManager) GetConnectionStatus() map[string]bool {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	status := make(map[string]bool)
	for name, client := range cm.clients {
		status[name] = client.Connected
	}
	return status
}

// ReconnectClient 重新連接指定的客戶端
func (cm *MCPClientManager) ReconnectClient(ctx context.Context, name string) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	client, exists := cm.clients[name]
	if !exists {
		return fmt.Errorf("client '%s' not found", name)
	}

	// 關閉現有連接
	if client.Session != nil {
		client.Session.Close()
	}

	// 重新初始化
	if err := cm.initializeClient(ctx, client.Config); err != nil {
		client.Connected = false
		client.LastError = err
		return fmt.Errorf("failed to reconnect client '%s': %w", name, err)
	}

	cm.logger.Debugf(ctx, "Successfully reconnected client '%s'", name)
	return nil
}
