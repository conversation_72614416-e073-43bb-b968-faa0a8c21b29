package mcp

import (
	"context"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

// MigrationSuggestion 遷移建議
type MigrationSuggestion struct {
	ServerName    string `json:"server_name"`
	CurrentType   string `json:"current_type"`
	SuggestedType string `json:"suggested_type"`
	Reason        string `json:"reason"`
	ConfigExample string `json:"config_example"`
}

// ConfigMigrationTool 配置遷移工具
type ConfigMigrationTool struct {
	logger glog.ILogger
}

// NewConfigMigrationTool 創建配置遷移工具
func NewConfigMigrationTool() *ConfigMigrationTool {
	return &ConfigMigrationTool{
		logger: g.Log().Cat("mcp-migration"),
	}
}

// AnalyzeConfiguration 分析配置並提供遷移建議
func (cmt *ConfigMigrationTool) AnalyzeConfiguration(ctx context.Context) ([]MigrationSuggestion, error) {
	cmt.logger.Debugf(ctx, "Analyzing MCP configuration for migration suggestions")

	configManager := NewConfigManager()
	config, err := configManager.LoadMCPConfig(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to load MCP config: %w", err)
	}

	if config == nil || !config.Enabled {
		cmt.logger.Debugf(ctx, "MCP is not enabled, no migration needed")
		return nil, nil
	}

	var suggestions []MigrationSuggestion

	for _, server := range config.Servers {
		if suggestion := cmt.analyzeSingleServer(server); suggestion != nil {
			suggestions = append(suggestions, *suggestion)
		}
	}

	cmt.logger.Debugf(ctx, "Generated %d migration suggestions", len(suggestions))
	return suggestions, nil
}

// analyzeSingleServer 分析單個服務器配置
func (cmt *ConfigMigrationTool) analyzeSingleServer(server MCPServerConfig) *MigrationSuggestion {
	switch server.Type {
	case TransportTypeHTTP:
		return &MigrationSuggestion{
			ServerName:    server.Name,
			CurrentType:   TransportTypeHTTP,
			SuggestedType: TransportTypeStreamable,
			Reason:        "HTTP transport is deprecated and not supported by official MCP Go SDK",
			ConfigExample: cmt.generateStreamableConfigExample(server),
		}
	default:
		// 其他協議不需要遷移
		return nil
	}
}

// generateStreamableConfigExample 生成Streamable配置示例
func (cmt *ConfigMigrationTool) generateStreamableConfigExample(server MCPServerConfig) string {
	var example strings.Builder
	
	example.WriteString("# Migrated configuration for server: " + server.Name + "\n")
	example.WriteString("servers:\n")
	example.WriteString("  - name: \"" + server.Name + "\"\n")
	example.WriteString("    type: \"streamable\"  # Changed from 'http' to 'streamable'\n")
	
	if server.URL != "" {
		example.WriteString("    url: \"" + server.URL + "\"\n")
	}
	
	if server.Timeout > 0 {
		example.WriteString(fmt.Sprintf("    timeout: %d\n", server.Timeout))
	}
	
	if len(server.Environment) > 0 {
		example.WriteString("    environment:\n")
		for key, value := range server.Environment {
			example.WriteString(fmt.Sprintf("      %s: \"%s\"\n", key, value))
		}
	}

	return example.String()
}

// GenerateMigrationReport 生成遷移報告
func (cmt *ConfigMigrationTool) GenerateMigrationReport(ctx context.Context) (string, error) {
	suggestions, err := cmt.AnalyzeConfiguration(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to analyze configuration: %w", err)
	}

	if len(suggestions) == 0 {
		return "✅ No MCP configuration migration needed. All servers use supported transport types.", nil
	}

	var report strings.Builder
	
	report.WriteString("# MCP Configuration Migration Report\n\n")
	report.WriteString(fmt.Sprintf("Found %d server(s) that need migration:\n\n", len(suggestions)))

	for i, suggestion := range suggestions {
		report.WriteString(fmt.Sprintf("## %d. Server: %s\n", i+1, suggestion.ServerName))
		report.WriteString(fmt.Sprintf("**Current Type**: %s\n", suggestion.CurrentType))
		report.WriteString(fmt.Sprintf("**Suggested Type**: %s\n", suggestion.SuggestedType))
		report.WriteString(fmt.Sprintf("**Reason**: %s\n\n", suggestion.Reason))
		
		report.WriteString("**Suggested Configuration**:\n")
		report.WriteString("```yaml\n")
		report.WriteString(suggestion.ConfigExample)
		report.WriteString("```\n\n")
		
		report.WriteString("**Migration Steps**:\n")
		report.WriteString("1. Update the `type` field from `http` to `streamable`\n")
		report.WriteString("2. Ensure the server endpoint supports the streamable HTTP transport\n")
		report.WriteString("3. Test the connection after migration\n")
		report.WriteString("4. Remove any HTTP-specific configurations if present\n\n")
		
		report.WriteString("---\n\n")
	}

	report.WriteString("## Additional Notes\n\n")
	report.WriteString("- The streamable HTTP transport provides better reliability and session management\n")
	report.WriteString("- It supports automatic reconnection and event replay\n")
	report.WriteString("- Most MCP servers that supported HTTP should also support streamable HTTP\n")
	report.WriteString("- If you encounter issues, check with your MCP server documentation\n\n")
	
	report.WriteString("## Need Help?\n\n")
	report.WriteString("If you need assistance with the migration, please:\n")
	report.WriteString("1. Check the MCP server documentation for supported transports\n")
	report.WriteString("2. Test the new configuration in a development environment first\n")
	report.WriteString("3. Contact support if you encounter compatibility issues\n")

	return report.String(), nil
}

// ValidatePostMigration 驗證遷移後的配置
func (cmt *ConfigMigrationTool) ValidatePostMigration(ctx context.Context) error {
	cmt.logger.Debugf(ctx, "Validating post-migration configuration")

	configManager := NewConfigManager()
	config, err := configManager.LoadMCPConfig(ctx)
	if err != nil {
		return fmt.Errorf("failed to load MCP config: %w", err)
	}

	if config == nil || !config.Enabled {
		cmt.logger.Debugf(ctx, "MCP is not enabled")
		return nil
	}

	var issues []string

	for _, server := range config.Servers {
		if server.Type == TransportTypeHTTP {
			issues = append(issues, fmt.Sprintf("Server '%s' still uses deprecated HTTP transport", server.Name))
		}
		
		adapter := NewTransportAdapter(server, &loggerAdapter{logger: cmt.logger})
		if err := adapter.ValidateConfig(); err != nil {
			issues = append(issues, fmt.Sprintf("Server '%s' has invalid config: %v", server.Name, err))
		}
	}

	if len(issues) > 0 {
		return fmt.Errorf("post-migration validation failed:\n- %s", strings.Join(issues, "\n- "))
	}

	cmt.logger.Debugf(ctx, "Post-migration validation passed")
	return nil
}

// GetDeprecationWarnings 獲取棄用警告
func (cmt *ConfigMigrationTool) GetDeprecationWarnings(ctx context.Context) []string {
	configManager := NewConfigManager()
	config, err := configManager.LoadMCPConfig(ctx)
	if err != nil {
		return []string{fmt.Sprintf("Failed to load MCP config: %v", err)}
	}

	if config == nil || !config.Enabled {
		return nil
	}

	var warnings []string

	for _, server := range config.Servers {
		if server.Type == TransportTypeHTTP {
			warning := fmt.Sprintf("⚠️  Server '%s' uses deprecated HTTP transport. Please migrate to 'streamable' transport for better compatibility with the official MCP Go SDK.", server.Name)
			warnings = append(warnings, warning)
		}
	}

	return warnings
}
