package mcp

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"brainHub/internal/llms/common"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/modelcontextprotocol/go-sdk/mcp"
)

// ToolDefinition 工具定義結構（統一格式）
type ToolDefinition = common.ToolDefinition

// ToolResult 工具調用結果結構（統一格式）
type ToolResult = common.ToolResult

// MCPError 自定義 MCP 錯誤類型（參考 mcpClient）
type MCPError struct {
	Operation string // 操作名稱
	Cause     error  // 原始錯誤
	Message   string // 錯誤訊息
}

// Error 實現 error 接口
func (e *MCPError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("MCP %s failed: %s (cause: %v)", e.Operation, e.Message, e.Cause)
	}
	return fmt.Sprintf("MCP %s failed: %s", e.Operation, e.Message)
}

// Unwrap 支援錯誤鏈
func (e *MCPError) Unwrap() error {
	return e.Cause
}

// NewMCPError 創建新的 MCP 錯誤
func NewMCPError(operation, message string, cause error) *MCPError {
	return &MCPError{
		Operation: operation,
		Message:   message,
		Cause:     cause,
	}
}

// UnifiedMCPClient 統一的 MCP 客戶端，參考 mcpClient 的成功實作
type UnifiedMCPClient struct {
	config     *MCPClientConfig
	client     *mcp.Client
	session    *mcp.ClientSession
	httpClient *http.Client
	logger     glog.ILogger
	mutex      sync.RWMutex
	connected  bool
	lastError  error
}

// MCPClientConfig 統一的客戶端配置結構
type MCPClientConfig struct {
	Name        string            `json:"name" yaml:"name"`
	ServerURL   string            `json:"server_url" yaml:"server_url"`
	Type        string            `json:"type" yaml:"type"`
	Headers     map[string]string `json:"headers" yaml:"headers"`
	Environment map[string]string `json:"environment" yaml:"environment"`
	Timeout     time.Duration     `json:"timeout" yaml:"timeout"`
	MaxRetries  int               `json:"max_retries" yaml:"max_retries"`
	AutoApprove []string          `json:"auto_approve" yaml:"auto_approve"`
	Disabled    bool              `json:"disabled" yaml:"disabled"`
}

// NewUnifiedMCPClient 創建新的統一 MCP 客戶端實例
func NewUnifiedMCPClient(config *MCPClientConfig) *UnifiedMCPClient {
	logger := g.Log().Cat("unified-mcp-client")

	// 設置默認值
	if config.Timeout == 0 {
		config.Timeout = 60 * time.Second
	}
	if config.MaxRetries == 0 {
		config.MaxRetries = 3
	}
	if config.Headers == nil {
		config.Headers = make(map[string]string)
	}
	if config.AutoApprove == nil {
		config.AutoApprove = []string{}
	}

	// 創建 MCP 客戶端實現
	impl := &mcp.Implementation{
		Name:    "brainHub-unified-mcp-client",
		Version: "v2.0.0",
	}

	// 創建 MCP 客戶端
	client := mcp.NewClient(impl, nil)

	return &UnifiedMCPClient{
		config: config,
		client: client,
		logger: logger,
	}
}

// Initialize 初始化 MCP 連接並進行握手
func (c *UnifiedMCPClient) Initialize(ctx context.Context) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.config.Disabled {
		c.logger.Debugf(ctx, "MCP client '%s' is disabled", c.config.Name)
		return nil
	}

	c.logger.Infof(ctx, "Initializing MCP client '%s' connection to %s", c.config.Name, c.config.ServerURL)

	// 驗證配置
	if err := c.validateConfig(); err != nil {
		c.lastError = NewMCPError("validation", "configuration validation failed", err)
		return c.lastError
	}

	// 使用重試機制初始化連接
	err := c.retryWithBackoff(ctx, "initialize", c.config.MaxRetries, func() error {
		return c.doInitialize(ctx)
	})

	if err != nil {
		c.lastError = err
		c.connected = false
		return err
	}

	c.connected = true
	c.lastError = nil
	c.logger.Infof(ctx, "MCP client '%s' initialized successfully", c.config.Name)
	return nil
}

// doInitialize 執行實際的初始化邏輯
func (c *UnifiedMCPClient) doInitialize(ctx context.Context) error {
	// 創建認證的 HTTP 客戶端（參考 mcpClient 的成功實作）
	c.httpClient = c.createAuthenticatedHTTPClient()

	// 創建傳輸層
	transport, err := c.createTransport(ctx)
	if err != nil {
		return fmt.Errorf("failed to create transport: %w", err)
	}

	// 建立連接 - 使用正確的 Connect 方法簽名
	connectCtx, cancel := context.WithTimeout(ctx, c.config.Timeout)
	defer cancel()

	session, err := c.client.Connect(connectCtx, transport, nil)
	if err != nil {
		return fmt.Errorf("failed to connect to MCP server: %w", err)
	}

	c.session = session
	c.logger.Debugf(ctx, "Successfully connected to MCP server '%s'", c.config.Name)

	// 測試連接 - 嘗試 Ping
	if err := c.session.Ping(ctx, nil); err != nil {
		c.logger.Warningf(ctx, "Failed to ping MCP server '%s': %v", c.config.Name, err)
		// Ping 失敗不阻止初始化，某些服務器可能不支持 Ping
	} else {
		c.logger.Debugf(ctx, "MCP server '%s' ping successful", c.config.Name)
	}

	return nil
}

// createTransport 創建傳輸層（參考 mcpClient 的實作）
func (c *UnifiedMCPClient) createTransport(ctx context.Context) (mcp.Transport, error) {
	switch c.config.Type {
	case TransportTypeSSE:
		return c.createSSETransport(ctx)
	case TransportTypeStreamable:
		return c.createStreamableTransport(ctx)
	case TransportTypeStdio:
		return nil, fmt.Errorf("STDIO transport not supported in unified client")
	default:
		return nil, fmt.Errorf("unsupported transport type: %s", c.config.Type)
	}
}

// createSSETransport 創建 SSE 傳輸（修復版本）
func (c *UnifiedMCPClient) createSSETransport(ctx context.Context) (mcp.Transport, error) {
	c.logger.Debugf(ctx, "Creating SSE transport for client '%s' with URL: %s",
		c.config.Name, c.config.ServerURL)

	// 關鍵修復：包含認證的 HTTP 客戶端
	return &mcp.SSEClientTransport{
		Endpoint:   c.config.ServerURL,
		HTTPClient: c.httpClient, // 這是關鍵！
	}, nil
}

// createStreamableTransport 創建 Streamable 傳輸（修復版本）
func (c *UnifiedMCPClient) createStreamableTransport(ctx context.Context) (mcp.Transport, error) {
	c.logger.Debugf(ctx, "Creating Streamable transport for client '%s' with URL: %s",
		c.config.Name, c.config.ServerURL)

	// 關鍵修復：包含認證的 HTTP 客戶端
	return &mcp.StreamableClientTransport{
		Endpoint:   c.config.ServerURL,
		HTTPClient: c.httpClient, // 這是關鍵！
	}, nil
}

// createAuthenticatedHTTPClient 創建包含認證標頭的 HTTP 客戶端（參考 mcpClient）
func (c *UnifiedMCPClient) createAuthenticatedHTTPClient() *http.Client {
	// 創建自定義 RoundTripper 來添加認證標頭
	transport := &http.Transport{}

	client := &http.Client{
		Timeout: c.config.Timeout,
		Transport: &authenticatedTransport{
			base:    transport,
			headers: c.config.Headers,
		},
	}

	return client
}

// authenticatedTransport 認證傳輸層（參考 mcpClient）
type authenticatedTransport struct {
	base    http.RoundTripper
	headers map[string]string
}

// RoundTrip 實現 http.RoundTripper 接口
func (at *authenticatedTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	// 複製請求以避免修改原始請求
	newReq := req.Clone(req.Context())

	// 添加認證標頭
	for key, value := range at.headers {
		newReq.Header.Set(key, value)
	}

	// 設置默認標頭
	if newReq.Header.Get("Content-Type") == "" {
		newReq.Header.Set("Content-Type", "application/json")
	}
	if newReq.Header.Get("Accept") == "" {
		newReq.Header.Set("Accept", "text/event-stream")
	}

	return at.base.RoundTrip(newReq)
}

// validateConfig 驗證配置（參考 mcpClient）
func (c *UnifiedMCPClient) validateConfig() error {
	if c.config.Name == "" {
		return fmt.Errorf("client name is required")
	}
	if c.config.ServerURL == "" {
		return fmt.Errorf("server URL is required")
	}
	if c.config.Type == "" {
		return fmt.Errorf("transport type is required")
	}
	if c.config.Timeout <= 0 {
		return fmt.Errorf("timeout must be positive")
	}
	return nil
}

// retryWithBackoff 帶退避的重試機制（參考 mcpClient）
func (c *UnifiedMCPClient) retryWithBackoff(ctx context.Context, operation string, maxRetries int, fn func() error) error {
	var lastErr error

	for attempt := 1; attempt <= maxRetries; attempt++ {
		err := fn()
		if err == nil {
			if attempt > 1 {
				c.logger.Infof(ctx, "Operation %s succeeded on attempt %d for client '%s'",
					operation, attempt, c.config.Name)
			}
			return nil
		}

		lastErr = err
		c.logger.Warningf(ctx, "Operation %s failed on attempt %d/%d for client '%s': %v",
			operation, attempt, maxRetries, c.config.Name, err)

		if attempt < maxRetries {
			// 指數退避：1s, 2s, 4s, 8s...
			backoffDuration := time.Duration(1<<(attempt-1)) * time.Second
			c.logger.Infof(ctx, "Retrying %s for client '%s' in %v...",
				operation, c.config.Name, backoffDuration)

			select {
			case <-ctx.Done():
				return NewMCPError(operation, "operation cancelled", ctx.Err())
			case <-time.After(backoffDuration):
				// 繼續重試
			}
		}
	}

	return NewMCPError(operation, fmt.Sprintf("failed after %d attempts", maxRetries), lastErr)
}

// IsConnected 檢查是否已連接（參考 mcpClient）
func (c *UnifiedMCPClient) IsConnected() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.connected && c.session != nil
}

// Close 關閉 MCP 連接（參考 mcpClient）
func (c *UnifiedMCPClient) Close() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.session != nil {
		c.logger.Infof(context.Background(), "Closing MCP session for client '%s'...", c.config.Name)
		err := c.session.Close()
		if err != nil {
			c.logger.Errorf(context.Background(), "Error closing MCP session for client '%s': %v",
				c.config.Name, err)
			return err
		}
		c.session = nil
		c.logger.Infof(context.Background(), "MCP session for client '%s' closed successfully", c.config.Name)
	}

	c.connected = false
	return nil
}

// GetToolDefinitions 獲取工具定義列表
func (c *UnifiedMCPClient) GetToolDefinitions(ctx context.Context) ([]ToolDefinition, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.connected || c.session == nil {
		return nil, fmt.Errorf("client '%s' is not connected", c.config.Name)
	}

	c.logger.Debugf(ctx, "Getting tool definitions from client '%s'", c.config.Name)

	// 添加超時控制（關鍵修復）
	toolCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// 調用 ListTools 方法
	result, err := c.session.ListTools(toolCtx, &mcp.ListToolsParams{})
	if err != nil {
		c.logger.Errorf(ctx, "Failed to list tools from client '%s': %v", c.config.Name, err)
		return nil, fmt.Errorf("failed to list tools: %w", err)
	}

	// 轉換為統一的工具定義格式
	var tools []ToolDefinition
	for _, tool := range result.Tools {
		// 轉換 InputSchema 為 map[string]interface{}
		var parameters map[string]interface{}
		if tool.InputSchema != nil {
			// 簡單轉換，實際使用中可能需要更複雜的轉換邏輯
			parameters = map[string]interface{}{
				"type":        "object",
				"description": tool.Description,
			}
		}

		toolDef := ToolDefinition{
			Name:        fmt.Sprintf("%s.%s", c.config.Name, tool.Name), // 添加客戶端前綴
			Description: tool.Description,
			Parameters:  parameters,    // 轉換後的參數
			ClientName:  c.config.Name, // 設置客戶端名稱
		}
		tools = append(tools, toolDef)
	}

	c.logger.Debugf(ctx, "Retrieved %d tools from client '%s'", len(tools), c.config.Name)
	return tools, nil
}

// CallTool 調用工具
func (c *UnifiedMCPClient) CallTool(ctx context.Context, toolName string, args map[string]interface{}) (*ToolResult, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.connected || c.session == nil {
		return &ToolResult{
			Success: false,
			Error:   fmt.Sprintf("Client '%s' is not connected", c.config.Name),
		}, nil
	}

	c.logger.Debugf(ctx, "Calling tool '%s' on client '%s' with args: %v", toolName, c.config.Name, args)

	// 添加超時控制
	toolCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// 使用官方 SDK 調用工具
	params := &mcp.CallToolParams{
		Name:      toolName,
		Arguments: args,
	}

	result, err := c.session.CallTool(toolCtx, params)
	if err != nil {
		c.logger.Errorf(ctx, "Tool call failed for '%s' on client '%s': %v", toolName, c.config.Name, err)
		return &ToolResult{
			Success: false,
			Error:   fmt.Sprintf("Tool execution error: %v", err),
		}, nil
	}

	// 轉換結果格式
	return c.convertResult(result), nil
}

// convertResult 轉換工具調用結果
func (c *UnifiedMCPClient) convertResult(result *mcp.CallToolResult) *ToolResult {
	if result == nil {
		return &ToolResult{
			Success: false,
			Error:   "Empty result from tool call",
		}
	}

	// 提取內容
	var content string
	if len(result.Content) > 0 {
		switch c := result.Content[0].(type) {
		case *mcp.TextContent:
			content = c.Text
		case *mcp.ImageContent:
			content = fmt.Sprintf("Image: %s", c.Data)
		default:
			content = fmt.Sprintf("Unknown content type: %T", c)
		}
	}

	// 檢查是否有錯誤
	success := !result.IsError
	var errorMsg string
	if result.IsError {
		errorMsg = content
		content = ""
	}

	return &ToolResult{
		Success: success,
		Content: content,
		Error:   errorMsg,
	}
}

// GetLastError 獲取最後一個錯誤
func (c *UnifiedMCPClient) GetLastError() error {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.lastError
}

// GetConfig 獲取客戶端配置
func (c *UnifiedMCPClient) GetConfig() *MCPClientConfig {
	return c.config
}

// Reconnect 重新連接
func (c *UnifiedMCPClient) Reconnect(ctx context.Context) error {
	c.logger.Infof(ctx, "Reconnecting MCP client '%s'...", c.config.Name)

	// 先關閉現有連接
	if err := c.Close(); err != nil {
		c.logger.Warningf(ctx, "Error closing existing connection for client '%s': %v", c.config.Name, err)
	}

	// 重新初始化
	return c.Initialize(ctx)
}
