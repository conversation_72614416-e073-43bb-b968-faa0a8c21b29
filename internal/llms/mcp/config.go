package mcp

import (
	"context"
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

// MCPConfig MCP配置結構
type MCPConfig struct {
	Enabled bool              `json:"enabled" yaml:"enabled"`
	Servers []MCPServerConfig `json:"servers" yaml:"servers"`
	Global  MCPGlobalConfig   `json:"global" yaml:"global"`
}

// MCPGlobalConfig 全局配置
type MCPGlobalConfig struct {
	Timeout     int               `json:"timeout,omitempty" yaml:"timeout,omitempty"`
	Environment map[string]string `json:"environment,omitempty" yaml:"environment,omitempty"`
}

// ConfigManager 配置管理器
type ConfigManager struct {
	cache       *MCPConfig
	cacheMutex  sync.RWMutex
	lastLoad    time.Time
	cacheExpiry time.Duration
	logger      glog.ILogger
}

// NewConfigManager 創建配置管理器
func NewConfigManager() *ConfigManager {
	return &ConfigManager{
		logger:      g.Log().Cat("mcp-config"),
		cacheExpiry: 5 * time.Minute, // 默認緩存5分鐘
	}
}

// LoadMCPConfig 加載MCP配置
func (cm *ConfigManager) LoadMCPConfig(ctx context.Context) (*MCPConfig, error) {
	cm.cacheMutex.Lock()
	defer cm.cacheMutex.Unlock()

	// 檢查緩存是否有效
	if cm.cache != nil && time.Since(cm.lastLoad) < cm.cacheExpiry {
		return cm.cache, nil
	}

	cm.logger.Debugf(ctx, "Loading MCP configuration from config files")

	// 嘗試從不同的配置路徑加載
	config, err := cm.loadFromConfigPaths(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to load MCP config: %w", err)
	}

	if config == nil {
		cm.logger.Debugf(ctx, "No MCP configuration found")
		return nil, nil
	}

	// 驗證和處理配置
	if err := cm.validateAndProcessConfig(ctx, config); err != nil {
		return nil, fmt.Errorf("invalid MCP config: %w", err)
	}

	// 更新緩存
	cm.cache = config
	cm.lastLoad = time.Now()

	cm.logger.Debugf(ctx, "MCP configuration loaded successfully: enabled=%v, servers=%d",
		config.Enabled, len(config.Servers))

	return config, nil
}

// loadFromConfigPaths 從不同的配置路徑加載
func (cm *ConfigManager) loadFromConfigPaths(ctx context.Context) (*MCPConfig, error) {
	// 配置路徑優先級
	configPaths := []string{
		"mcp_config",
		"mcp_servers",
		"mcp",
	}

	for _, path := range configPaths {
		if config, err := g.Cfg().Get(ctx, path); err == nil && config != nil {
			cm.logger.Debugf(ctx, "Found MCP config at path: %s", path)

			var mcpConfig MCPConfig
			if err := config.Scan(&mcpConfig); err != nil {
				cm.logger.Warningf(ctx, "Failed to parse MCP config from path '%s': %v", path, err)
				continue
			}

			return &mcpConfig, nil
		}
	}

	return nil, nil
}

// validateAndProcessConfig 驗證和處理配置
func (cm *ConfigManager) validateAndProcessConfig(ctx context.Context, config *MCPConfig) error {
	if !config.Enabled {
		return nil
	}

	// 處理服務器配置
	for i := range config.Servers {
		server := &config.Servers[i]

		// 環境變量展開
		if err := cm.expandEnvironmentVariables(server); err != nil {
			return fmt.Errorf("failed to expand environment variables for server '%s': %w", server.Name, err)
		}

		// 應用全局配置
		cm.applyGlobalConfig(server, &config.Global)

		// 驗證服務器配置
		if err := cm.validateServerConfig(ctx, server); err != nil {
			return fmt.Errorf("invalid server config '%s': %w", server.Name, err)
		}
	}

	return nil
}

// expandEnvironmentVariables 展開環境變量
func (cm *ConfigManager) expandEnvironmentVariables(server *MCPServerConfig) error {
	// 展開命令
	if server.Command != "" {
		server.Command = os.ExpandEnv(server.Command)
	}

	// 展開參數
	for i, arg := range server.Args {
		server.Args[i] = os.ExpandEnv(arg)
	}

	// 展開URL
	if server.URL != "" {
		server.URL = os.ExpandEnv(server.URL)
	}

	// 展開環境變量值
	for key, value := range server.Environment {
		server.Environment[key] = os.ExpandEnv(value)
	}

	return nil
}

// applyGlobalConfig 應用全局配置
func (cm *ConfigManager) applyGlobalConfig(server *MCPServerConfig, global *MCPGlobalConfig) {
	// 應用全局超時設置
	if server.Timeout == 0 && global.Timeout > 0 {
		server.Timeout = global.Timeout
	}

	// 合併環境變量
	if len(global.Environment) > 0 {
		if server.Environment == nil {
			server.Environment = make(map[string]string)
		}
		for key, value := range global.Environment {
			if _, exists := server.Environment[key]; !exists {
				server.Environment[key] = value
			}
		}
	}
}

// validateServerConfig 驗證服務器配置
func (cm *ConfigManager) validateServerConfig(ctx context.Context, server *MCPServerConfig) error {
	if server.Name == "" {
		return fmt.Errorf("server name is required")
	}

	// 檢查HTTP協議並提供遷移建議
	if server.Type == TransportTypeHTTP {
		cm.logger.Warningf(ctx, "Server '%s' uses deprecated HTTP transport. Please migrate to 'streamable' transport for better compatibility.", server.Name)
		return fmt.Errorf("HTTP transport is deprecated, please migrate to streamable transport")
	}

	switch server.Type {
	case TransportTypeStdio:
		if server.Command == "" {
			return fmt.Errorf("command is required for stdio transport")
		}
	case TransportTypeSSE, TransportTypeStreamable:
		if server.URL == "" {
			return fmt.Errorf("URL is required for %s transport", server.Type)
		}
	default:
		return fmt.Errorf("unsupported transport type: %s", server.Type)
	}

	return nil
}

// ClearCache 清除配置緩存
func (cm *ConfigManager) ClearCache() {
	cm.cacheMutex.Lock()
	defer cm.cacheMutex.Unlock()

	cm.cache = nil
	cm.lastLoad = time.Time{}
}

// GetMigrationSuggestions 獲取配置遷移建議
func (cm *ConfigManager) GetMigrationSuggestions(ctx context.Context) []string {
	config, err := cm.LoadMCPConfig(ctx)
	if err != nil || config == nil {
		return nil
	}

	var suggestions []string

	for _, server := range config.Servers {
		if server.Type == TransportTypeHTTP {
			suggestion := fmt.Sprintf("Server '%s': Migrate from 'http' to 'streamable' transport. Change type to 'streamable' and ensure URL is accessible.", server.Name)
			suggestions = append(suggestions, suggestion)
		}
	}

	return suggestions
}

// ValidateConfig 驗證MCP配置
func (cm *ConfigManager) ValidateConfig(config *MCPConfig) error {
	if config == nil {
		return nil // nil配置是有效的（MCP未啟用）
	}

	if !config.Enabled {
		return nil // 未啟用的配置是有效的
	}

	if len(config.Servers) == 0 {
		return fmt.Errorf("no servers configured but MCP is enabled")
	}

	// 驗證每個服務器配置
	for _, server := range config.Servers {
		adapter := NewTransportAdapter(server, &loggerAdapter{logger: cm.logger})
		if err := adapter.ValidateConfig(); err != nil {
			return fmt.Errorf("invalid server configuration for '%s': %w", server.Name, err)
		}
	}

	return nil
}
