package mcp

import (
	"context"
	"fmt"

	"brainHub/internal/llms/common"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

// LLMIntegration LLM集成接口
type LLMIntegration struct {
	toolManager *MCPToolManager
	logger      glog.ILogger
}

// NewLLMIntegration 創建LLM集成實例
func NewLLMIntegration(ctx context.Context) (*LLMIntegration, error) {
	logger := g.Log().Cat("mcp-integration")

	// 從配置文件加載MCP配置
	configManager := NewConfigManager()
	mcpConfig, err := configManager.LoadMCPConfig(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to load MCP config: %w", err)
	}

	if mcpConfig == nil || !mcpConfig.Enabled {
		logger.Debugf(ctx, "MCP is disabled or not configured")
		return nil, nil // MCP未啟用，返回nil但不報錯
	}

	// 檢查並顯示遷移警告
	warnings := GetDeprecationWarnings(ctx)
	for _, warning := range warnings {
		logger.Warningf(ctx, warning)
	}

	// 創建工具管理器
	toolManager := NewMCPToolManager(mcpConfig, &loggerAdapter{logger: logger})
	if err := toolManager.Initialize(ctx); err != nil {
		return nil, fmt.Errorf("failed to initialize MCP tool manager: %w", err)
	}

	logger.Debugf(ctx, "MCP integration initialized successfully")

	return &LLMIntegration{
		toolManager: toolManager,
		logger:      logger,
	}, nil
}

// GetToolDefinitions 獲取工具定義
func (li *LLMIntegration) GetToolDefinitions(ctx context.Context) ([]common.ToolDefinition, error) {
	if li.toolManager == nil {
		return nil, nil
	}
	return li.toolManager.GetToolDefinitions(ctx)
}

// CallTool 調用工具
func (li *LLMIntegration) CallTool(ctx context.Context, toolName string, args map[string]interface{}) (*common.ToolResult, error) {
	if li.toolManager == nil {
		return nil, fmt.Errorf("MCP not initialized")
	}
	return li.toolManager.CallTool(ctx, toolName, args)
}

// Close 關閉集成
func (li *LLMIntegration) Close(ctx context.Context) error {
	if li.toolManager == nil {
		return nil
	}
	return li.toolManager.Close(ctx)
}

// GetConnectionStatus 獲取連接狀態
func (li *LLMIntegration) GetConnectionStatus() map[string]bool {
	if li.toolManager == nil {
		return nil
	}
	return li.toolManager.GetConnectionStatus()
}

// ReconnectClient 重新連接客戶端
func (li *LLMIntegration) ReconnectClient(ctx context.Context, clientName string) error {
	if li.toolManager == nil {
		return fmt.Errorf("MCP not initialized")
	}
	return li.toolManager.ReconnectClient(ctx, clientName)
}

// IsEnabled 檢查MCP是否啟用
func (li *LLMIntegration) IsEnabled() bool {
	return li.toolManager != nil
}

// loggerAdapter 適配器，將glog.ILogger適配為Logger接口
type loggerAdapter struct {
	logger glog.ILogger
}

func (la *loggerAdapter) Warningf(ctx context.Context, format string, args ...interface{}) {
	la.logger.Warningf(ctx, format, args...)
}

func (la *loggerAdapter) Errorf(ctx context.Context, format string, args ...interface{}) {
	la.logger.Errorf(ctx, format, args...)
}

func (la *loggerAdapter) Debugf(ctx context.Context, format string, args ...interface{}) {
	la.logger.Debugf(ctx, format, args...)
}

// GetMigrationSuggestions 獲取配置遷移建議
func GetMigrationSuggestions(ctx context.Context) []string {
	configManager := NewConfigManager()
	return configManager.GetMigrationSuggestions(ctx)
}

// ValidateConfiguration 驗證MCP配置
func ValidateConfiguration(ctx context.Context) error {
	configManager := NewConfigManager()
	config, err := configManager.LoadMCPConfig(ctx)
	if err != nil {
		return fmt.Errorf("failed to load MCP config: %w", err)
	}

	if config == nil {
		return nil // 沒有配置不算錯誤
	}

	if !config.Enabled {
		return nil // 未啟用不算錯誤
	}

	// 檢查是否有有效的服務器配置
	validServers := 0
	for _, server := range config.Servers {
		adapter := NewTransportAdapter(server, &loggerAdapter{logger: g.Log().Cat("mcp-validation")})
		if err := adapter.ValidateConfig(); err != nil {
			g.Log().Cat("mcp-validation").Warningf(ctx, "Server '%s' has invalid config: %v", server.Name, err)
			continue
		}
		validServers++
	}

	if validServers == 0 {
		return fmt.Errorf("no valid MCP servers configured")
	}

	g.Log().Cat("mcp-validation").Debugf(ctx, "MCP configuration validation passed: %d valid servers", validServers)
	return nil
}

// GenerateMigrationReport 生成配置遷移報告
func GenerateMigrationReport(ctx context.Context) (string, error) {
	migrationTool := NewConfigMigrationTool()
	return migrationTool.GenerateMigrationReport(ctx)
}

// GetDeprecationWarnings 獲取棄用警告
func GetDeprecationWarnings(ctx context.Context) []string {
	migrationTool := NewConfigMigrationTool()
	return migrationTool.GetDeprecationWarnings(ctx)
}

// ValidatePostMigration 驗證遷移後的配置
func ValidatePostMigration(ctx context.Context) error {
	migrationTool := NewConfigMigrationTool()
	return migrationTool.ValidatePostMigration(ctx)
}
