package mcp

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"brainHub/internal/llms/common"

	"github.com/modelcontextprotocol/go-sdk/mcp"
)

// MCPToolManager 新的工具管理器
type MCPToolManager struct {
	clientManager *MCPClientManager
	config        *MCPConfig
	logger        Logger
}

// NewMCPToolManager 創建新的工具管理器
func NewMCPToolManager(config *MCPConfig, logger Logger) *MCPToolManager {
	clientManager := NewMCPClientManager(config, logger)
	return &MCPToolManager{
		clientManager: clientManager,
		config:        config,
		logger:        logger,
	}
}

// Initialize 初始化工具管理器
func (m *MCPToolManager) Initialize(ctx context.Context) error {
	m.logger.Debugf(ctx, "Initializing MCP tool manager")

	if err := m.clientManager.Initialize(ctx); err != nil {
		return fmt.Errorf("failed to initialize client manager: %w", err)
	}

	m.logger.Debugf(ctx, "MCP tool manager initialized successfully")
	return nil
}

// GetToolDefinitions 獲取所有工具定義
func (m *MCPToolManager) GetToolDefinitions(ctx context.Context) ([]common.ToolDefinition, error) {
	m.logger.Debugf(ctx, "Getting tool definitions from all MCP clients")

	var allTools []common.ToolDefinition
	clients := m.clientManager.GetAllClients()

	for clientName, clientInfo := range clients {
		if !clientInfo.Connected {
			m.logger.Warningf(ctx, "Client '%s' is not connected, skipping tool definitions", clientName)
			continue
		}

		tools, err := m.getToolDefinitionsFromClient(ctx, clientName, clientInfo)
		if err != nil {
			m.logger.Errorf(ctx, "Failed to get tool definitions from client '%s': %v", clientName, err)
			continue
		}

		allTools = append(allTools, tools...)
		m.logger.Debugf(ctx, "Retrieved %d tools from client '%s'", len(tools), clientName)
	}

	m.logger.Debugf(ctx, "Total tool definitions retrieved: %d", len(allTools))
	return allTools, nil
}

// getToolDefinitionsFromClient 從指定客戶端獲取工具定義
func (m *MCPToolManager) getToolDefinitionsFromClient(ctx context.Context, clientName string, clientInfo *ClientInfo) ([]common.ToolDefinition, error) {
	// 添加超時控制
	toolCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// 使用官方SDK獲取工具列表
	params := &mcp.ListToolsParams{}
	result, err := clientInfo.Session.ListTools(toolCtx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to list tools: %w", err)
	}

	var toolDefinitions []common.ToolDefinition
	for _, tool := range result.Tools {
		// 轉換為內部格式
		var parameters map[string]interface{}
		if tool.InputSchema != nil {
			// 將 jsonschema.Schema 序列化為 JSON 後再反序列化為 map[string]interface{}
			if b, err := json.Marshal(tool.InputSchema); err != nil {
				m.logger.Warningf(ctx, "Failed to marshal tool schema for '%s.%s': %v", clientName, tool.Name, err)
			} else {
				if err := json.Unmarshal(b, &parameters); err != nil {
					m.logger.Warningf(ctx, "Failed to unmarshal tool schema for '%s.%s': %v", clientName, tool.Name, err)
				}
			}
		}

		toolDef := common.ToolDefinition{
			Name:        fmt.Sprintf("%s.%s", clientName, tool.Name),
			Description: tool.Description,
			Parameters:  parameters,
			ClientName:  clientName,
		}
		toolDefinitions = append(toolDefinitions, toolDef)
	}

	return toolDefinitions, nil
}

// CallTool 調用工具
func (m *MCPToolManager) CallTool(ctx context.Context, toolName string, args map[string]interface{}) (*common.ToolResult, error) {
	m.logger.Debugf(ctx, "Calling tool: %s with args: %v", toolName, args)

	// 解析工具名稱
	clientName, actualToolName, err := m.parseToolName(toolName)
	if err != nil {
		return &common.ToolResult{
			Success: false,
			Error:   fmt.Sprintf("Invalid tool name format: %v", err),
		}, nil
	}

	// 獲取客戶端
	clientInfo, exists := m.clientManager.GetClient(clientName)
	if !exists {
		return &common.ToolResult{
			Success: false,
			Error:   fmt.Sprintf("Client '%s' not found", clientName),
		}, nil
	}

	if !clientInfo.Connected {
		return &common.ToolResult{
			Success: false,
			Error:   fmt.Sprintf("Client '%s' is not connected", clientName),
		}, nil
	}

	// 添加超時控制
	toolCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// 使用官方SDK調用工具
	params := &mcp.CallToolParams{
		Name:      actualToolName,
		Arguments: args,
	}

	result, err := clientInfo.Session.CallTool(toolCtx, params)
	if err != nil {
		m.logger.Errorf(ctx, "Tool call failed for '%s': %v", toolName, err)
		return &common.ToolResult{
			Success: false,
			Error:   fmt.Sprintf("Tool execution error: %v", err),
		}, nil
	}

	// 轉換結果格式
	return m.convertResult(result), nil
}

// parseToolName 解析工具名稱，格式為 "clientName.toolName"
func (m *MCPToolManager) parseToolName(toolName string) (clientName, actualToolName string, err error) {
	parts := strings.SplitN(toolName, ".", 2)
	if len(parts) != 2 {
		return "", "", fmt.Errorf("tool name must be in format 'clientName.toolName', got: %s", toolName)
	}
	return parts[0], parts[1], nil
}

// convertResult 轉換官方SDK結果為內部格式
func (m *MCPToolManager) convertResult(result *mcp.CallToolResult) *common.ToolResult {
	if result.IsError {
		return &common.ToolResult{
			Success: false,
			Error:   "Tool execution failed",
		}
	}

	// 處理內容
	var content strings.Builder
	for i, c := range result.Content {
		if i > 0 {
			content.WriteString("\n")
		}

		// 根據內容類型處理
		switch contentItem := c.(type) {
		case *mcp.TextContent:
			content.WriteString(contentItem.Text)
		default:
			// 對於其他類型的內容，嘗試轉換為字符串
			content.WriteString(fmt.Sprintf("%v", contentItem))
		}
	}

	return &common.ToolResult{
		Success: true,
		Content: content.String(),
	}
}

// Close 關閉工具管理器
func (m *MCPToolManager) Close(ctx context.Context) error {
	m.logger.Debugf(ctx, "Closing MCP tool manager")

	if err := m.clientManager.Close(ctx); err != nil {
		return fmt.Errorf("failed to close client manager: %w", err)
	}

	m.logger.Debugf(ctx, "MCP tool manager closed successfully")
	return nil
}

// GetConnectionStatus 獲取所有客戶端的連接狀態
func (m *MCPToolManager) GetConnectionStatus() map[string]bool {
	return m.clientManager.GetConnectionStatus()
}

// ReconnectClient 重新連接指定的客戶端
func (m *MCPToolManager) ReconnectClient(ctx context.Context, clientName string) error {
	return m.clientManager.ReconnectClient(ctx, clientName)
}
