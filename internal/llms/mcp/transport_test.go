package mcp

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// mockLogger 模擬日誌記錄器
type mockLogger struct {
	warnings []string
	errors   []string
	debugs   []string
}

func (m *mockLogger) Warningf(ctx context.Context, format string, args ...interface{}) {
	m.warnings = append(m.warnings, format)
}

func (m *mockLogger) Errorf(ctx context.Context, format string, args ...interface{}) {
	m.errors = append(m.errors, format)
}

func (m *mockLogger) Debugf(ctx context.Context, format string, args ...interface{}) {
	m.debugs = append(m.debugs, format)
}

func TestTransportAdapter_ValidateConfig(t *testing.T) {
	tests := []struct {
		name        string
		config      MCPServerConfig
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid stdio config",
			config: MCPServerConfig{
				Name:    "test-stdio",
				Type:    TransportTypeStdio,
				Command: "test-command",
				Args:    []string{"arg1", "arg2"},
			},
			expectError: false,
		},
		{
			name: "valid sse config",
			config: MCPServerConfig{
				Name: "test-sse",
				Type: TransportTypeSSE,
				URL:  "http://localhost:8080",
			},
			expectError: false,
		},
		{
			name: "valid streamable config",
			config: MCPServerConfig{
				Name: "test-streamable",
				Type: TransportTypeStreamable,
				URL:  "http://localhost:8080",
			},
			expectError: false,
		},
		{
			name: "deprecated http config",
			config: MCPServerConfig{
				Name: "test-http",
				Type: TransportTypeHTTP,
				URL:  "http://localhost:8080",
			},
			expectError: true,
			errorMsg:    "HTTP transport is deprecated",
		},
		{
			name: "missing server name",
			config: MCPServerConfig{
				Type:    TransportTypeStdio,
				Command: "test-command",
			},
			expectError: true,
			errorMsg:    "server name is required",
		},
		{
			name: "stdio missing command",
			config: MCPServerConfig{
				Name: "test-stdio",
				Type: TransportTypeStdio,
			},
			expectError: true,
			errorMsg:    "command is required for stdio transport",
		},
		{
			name: "sse missing url",
			config: MCPServerConfig{
				Name: "test-sse",
				Type: TransportTypeSSE,
			},
			expectError: true,
			errorMsg:    "URL is required for sse transport",
		},
		{
			name: "unsupported transport type",
			config: MCPServerConfig{
				Name: "test-unknown",
				Type: "unknown",
			},
			expectError: true,
			errorMsg:    "unsupported transport type",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			logger := &mockLogger{}
			adapter := NewTransportAdapter(tt.config, logger)
			
			err := adapter.ValidateConfig()
			
			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestTransportAdapter_CreateTransport(t *testing.T) {
	ctx := context.Background()
	logger := &mockLogger{}

	t.Run("create stdio transport", func(t *testing.T) {
		config := MCPServerConfig{
			Name:    "test-stdio",
			Type:    TransportTypeStdio,
			Command: "echo",
			Args:    []string{"hello"},
		}
		
		adapter := NewTransportAdapter(config, logger)
		transport, err := adapter.CreateTransport(ctx)
		
		assert.NoError(t, err)
		assert.NotNil(t, transport)
	})

	t.Run("create sse transport", func(t *testing.T) {
		config := MCPServerConfig{
			Name: "test-sse",
			Type: TransportTypeSSE,
			URL:  "http://localhost:8080",
		}
		
		adapter := NewTransportAdapter(config, logger)
		transport, err := adapter.CreateTransport(ctx)
		
		assert.NoError(t, err)
		assert.NotNil(t, transport)
	})

	t.Run("create streamable transport", func(t *testing.T) {
		config := MCPServerConfig{
			Name: "test-streamable",
			Type: TransportTypeStreamable,
			URL:  "http://localhost:8080",
		}
		
		adapter := NewTransportAdapter(config, logger)
		transport, err := adapter.CreateTransport(ctx)
		
		assert.NoError(t, err)
		assert.NotNil(t, transport)
	})

	t.Run("http transport shows warning", func(t *testing.T) {
		config := MCPServerConfig{
			Name: "test-http",
			Type: TransportTypeHTTP,
			URL:  "http://localhost:8080",
		}
		
		adapter := NewTransportAdapter(config, logger)
		transport, err := adapter.CreateTransport(ctx)
		
		assert.Error(t, err)
		assert.Nil(t, transport)
		assert.Contains(t, err.Error(), "HTTP transport not supported")
		assert.Len(t, logger.warnings, 1)
		assert.Contains(t, logger.warnings[0], "HTTP transport is deprecated")
	})

	t.Run("unsupported transport type", func(t *testing.T) {
		config := MCPServerConfig{
			Name: "test-unknown",
			Type: "unknown",
		}
		
		adapter := NewTransportAdapter(config, logger)
		transport, err := adapter.CreateTransport(ctx)
		
		assert.Error(t, err)
		assert.Nil(t, transport)
		assert.Contains(t, err.Error(), "unsupported transport type")
	})
}

func TestTransportAdapter_EnvironmentVariables(t *testing.T) {
	ctx := context.Background()
	logger := &mockLogger{}

	config := MCPServerConfig{
		Name:    "test-stdio-env",
		Type:    TransportTypeStdio,
		Command: "echo",
		Args:    []string{"hello"},
		Environment: map[string]string{
			"TEST_VAR": "test_value",
			"PATH":     "/usr/bin:/bin",
		},
	}

	adapter := NewTransportAdapter(config, logger)
	transport, err := adapter.CreateTransport(ctx)

	require.NoError(t, err)
	require.NotNil(t, transport)

	// 驗證環境變量設置（這裡只能驗證沒有錯誤，實際環境變量設置在CommandTransport內部）
	assert.Len(t, logger.debugs, 1)
	assert.Contains(t, logger.debugs[0], "Creating STDIO transport")
}
