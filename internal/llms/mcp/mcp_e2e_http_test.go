package mcp_test

import (
	"context"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	bhmcp "brainHub/internal/llms/mcp"

	mcpgo "github.com/modelcontextprotocol/go-sdk/mcp"
)

// testLogger
//
// NOTE: English log output per project guideline
// Implements bhmcp.Logger for testing purpose
type testLogger struct{ t *testing.T }

func (l *testLogger) Warningf(ctx context.Context, format string, args ...interface{}) {
	if l != nil && l.t != nil {
		l.t.Logf("[WARN] "+format, args...)
	}
}
func (l *testLogger) Errorf(ctx context.Context, format string, args ...interface{}) {
	if l != nil && l.t != nil {
		l.t.Logf("[ERROR] "+format, args...)
	}
}
func (l *testLogger) Debugf(ctx context.Context, format string, args ...interface{}) {
	if l != nil && l.t != nil {
		l.t.Logf("[DEBUG] "+format, args...)
	}
}

// buildTestServer creates a minimal MCP server exposing a simple echo tool.
func buildTestServer() *mcpgo.Server {
	srv := mcpgo.NewServer(&mcpgo.Implementation{Name: "e2e-server", Version: "v1.0.0"}, nil)

	type EchoInput struct {
		Text string `json:"text" jsonschema:"text to echo back"`
	}

	mcpgo.AddTool(srv, &mcpgo.Tool{
		Name:        "echo",
		Description: "Echo back the provided text",
	}, func(ctx context.Context, req *mcpgo.CallToolRequest, input EchoInput) (*mcpgo.CallToolResult, struct{}, error) {
		return &mcpgo.CallToolResult{Content: []mcpgo.Content{&mcpgo.TextContent{Text: "Echo: " + input.Text}}}, struct{}{}, nil
	})

	return srv
}

func TestMCP_E2E_HTTP(t *testing.T) {
	ctx := context.Background()

	mux := http.NewServeMux()

	// Only use Streamable handler (SSE handler not available in current SDK version)
	streamHandler := mcpgo.NewStreamableHTTPHandler(func(r *http.Request) *mcpgo.Server { return buildTestServer() }, nil)
	mux.Handle("/", streamHandler)

	ts := httptest.NewServer(mux)
	defer ts.Close()

	cfg := &bhmcp.MCPConfig{
		Enabled: true,
		Servers: []bhmcp.MCPServerConfig{
			{Name: "e2e-streamable", Type: bhmcp.TransportTypeStreamable, URL: ts.URL},
		},
		Global: bhmcp.MCPGlobalConfig{Timeout: 10},
	}

	mgr := bhmcp.NewMCPToolManager(cfg, &testLogger{t: t})
	if err := mgr.Initialize(ctx); err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}
	defer mgr.Close(ctx)

	// Debug: check connection status
	statuses := mgr.GetConnectionStatus()
	t.Logf("connection statuses: %+v", statuses)

	defs, err := mgr.GetToolDefinitions(ctx)
	if err != nil {
		t.Fatalf("GetToolDefinitions failed: %v", err)
	}

	names := map[string]bool{}
	for _, d := range defs {
		names[d.Name] = true
	}
	if !names["e2e-streamable.echo"] {
		t.Fatalf("expected streamable echo tool, got names=%v", names)
	}

	res, err := mgr.CallTool(ctx, "e2e-streamable.echo", map[string]interface{}{"text": "world"})
	if err != nil || res == nil || !res.Success || !strings.Contains(res.Content, "Echo: world") {
		t.Fatalf("Streamable echo failed: err=%v, res=%+v", err, res)
	}
}
