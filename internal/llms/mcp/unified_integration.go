package mcp

import (
	"context"
	"fmt"

	"brainHub/internal/llms/common"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

// UnifiedMCPIntegration 統一的 MCP 集成接口，替代複雜的 LLMIntegration
type UnifiedMCPIntegration struct {
	manager *UnifiedMCPManager
	logger  glog.ILogger
}

// NewUnifiedMCPIntegration 創建統一的 MCP 集成實例
func NewUnifiedMCPIntegration(ctx context.Context) (*UnifiedMCPIntegration, error) {
	logger := g.Log().Cat("unified-mcp-integration")

	// 從配置文件載入 MCP 配置
	config, err := LoadUnifiedMCPConfig(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to load unified MCP config: %w", err)
	}

	if config == nil || !config.Enabled {
		logger.Debugf(ctx, "MCP is disabled or not configured")
		return nil, nil // MCP 未啟用，返回 nil 但不報錯
	}

	// 創建統一管理器
	manager := NewUnifiedMCPManager(config)
	if err := manager.Initialize(ctx); err != nil {
		return nil, fmt.Errorf("failed to initialize unified MCP manager: %w", err)
	}

	logger.Debugf(ctx, "Unified MCP integration initialized successfully with %d clients", 
		manager.GetClientCount())

	return &UnifiedMCPIntegration{
		manager: manager,
		logger:  logger,
	}, nil
}

// GetToolDefinitions 獲取工具定義
func (ui *UnifiedMCPIntegration) GetToolDefinitions(ctx context.Context) ([]common.ToolDefinition, error) {
	if ui.manager == nil {
		return nil, nil
	}
	return ui.manager.GetToolDefinitions(ctx)
}

// CallTool 調用工具
func (ui *UnifiedMCPIntegration) CallTool(ctx context.Context, toolName string, args map[string]interface{}) (*common.ToolResult, error) {
	if ui.manager == nil {
		return nil, fmt.Errorf("MCP not initialized")
	}
	return ui.manager.CallTool(ctx, toolName, args)
}

// Close 關閉集成
func (ui *UnifiedMCPIntegration) Close(ctx context.Context) error {
	if ui.manager == nil {
		return nil
	}
	return ui.manager.Close(ctx)
}

// GetConnectionStatus 獲取連接狀態
func (ui *UnifiedMCPIntegration) GetConnectionStatus() map[string]bool {
	if ui.manager == nil {
		return nil
	}
	return ui.manager.GetConnectionStatus()
}

// ReconnectClient 重新連接客戶端
func (ui *UnifiedMCPIntegration) ReconnectClient(ctx context.Context, clientName string) error {
	if ui.manager == nil {
		return fmt.Errorf("MCP not initialized")
	}
	return ui.manager.ReconnectClient(ctx, clientName)
}

// IsEnabled 檢查 MCP 是否啟用
func (ui *UnifiedMCPIntegration) IsEnabled() bool {
	return ui.manager != nil && ui.manager.IsEnabled()
}

// GetClientCount 獲取客戶端數量
func (ui *UnifiedMCPIntegration) GetClientCount() int {
	if ui.manager == nil {
		return 0
	}
	return ui.manager.GetClientCount()
}

// GetManager 獲取底層管理器（用於高級操作）
func (ui *UnifiedMCPIntegration) GetManager() *UnifiedMCPManager {
	return ui.manager
}

// HealthCheck 健康檢查
func (ui *UnifiedMCPIntegration) HealthCheck(ctx context.Context) map[string]interface{} {
	if ui.manager == nil {
		return map[string]interface{}{
			"enabled": false,
			"status":  "disabled",
		}
	}

	connectionStatus := ui.manager.GetConnectionStatus()
	connectedCount := 0
	totalCount := len(connectionStatus)

	for _, connected := range connectionStatus {
		if connected {
			connectedCount++
		}
	}

	status := "healthy"
	if connectedCount == 0 && totalCount > 0 {
		status = "unhealthy"
	} else if connectedCount < totalCount {
		status = "degraded"
	}

	return map[string]interface{}{
		"enabled":          ui.manager.IsEnabled(),
		"status":           status,
		"total_clients":    totalCount,
		"connected_clients": connectedCount,
		"connection_status": connectionStatus,
	}
}

// GetToolCount 獲取工具總數
func (ui *UnifiedMCPIntegration) GetToolCount(ctx context.Context) (int, error) {
	if ui.manager == nil {
		return 0, nil
	}

	tools, err := ui.manager.GetToolDefinitions(ctx)
	if err != nil {
		return 0, err
	}

	return len(tools), nil
}

// ListClients 列出所有客戶端信息
func (ui *UnifiedMCPIntegration) ListClients() []map[string]interface{} {
	if ui.manager == nil {
		return nil
	}

	connectionStatus := ui.manager.GetConnectionStatus()
	var clients []map[string]interface{}

	for clientName, connected := range connectionStatus {
		client, exists := ui.manager.GetClient(clientName)
		if !exists {
			continue
		}

		config := client.GetConfig()
		clientInfo := map[string]interface{}{
			"name":       config.Name,
			"type":       config.Type,
			"url":        config.ServerURL,
			"connected":  connected,
			"disabled":   config.Disabled,
			"timeout":    config.Timeout.String(),
		}

		if lastError := client.GetLastError(); lastError != nil {
			clientInfo["last_error"] = lastError.Error()
		}

		clients = append(clients, clientInfo)
	}

	return clients
}

// TestConnection 測試指定客戶端的連接
func (ui *UnifiedMCPIntegration) TestConnection(ctx context.Context, clientName string) error {
	if ui.manager == nil {
		return fmt.Errorf("MCP not initialized")
	}

	client, exists := ui.manager.GetClient(clientName)
	if !exists {
		return fmt.Errorf("client '%s' not found", clientName)
	}

	if !client.IsConnected() {
		return fmt.Errorf("client '%s' is not connected", clientName)
	}

	// 嘗試獲取工具列表來測試連接
	_, err := client.GetToolDefinitions(ctx)
	if err != nil {
		return fmt.Errorf("connection test failed for client '%s': %w", clientName, err)
	}

	ui.logger.Debugf(ctx, "Connection test successful for client '%s'", clientName)
	return nil
}

// GetClientStats 獲取客戶端統計信息
func (ui *UnifiedMCPIntegration) GetClientStats(ctx context.Context) map[string]interface{} {
	if ui.manager == nil {
		return map[string]interface{}{
			"enabled": false,
		}
	}

	connectionStatus := ui.manager.GetConnectionStatus()
	stats := map[string]interface{}{
		"enabled":       ui.manager.IsEnabled(),
		"total_clients": len(connectionStatus),
		"clients":       make(map[string]interface{}),
	}

	connectedCount := 0
	for clientName, connected := range connectionStatus {
		if connected {
			connectedCount++
		}

		client, exists := ui.manager.GetClient(clientName)
		if !exists {
			continue
		}

		clientStats := map[string]interface{}{
			"connected": connected,
			"type":      client.GetConfig().Type,
			"url":       client.GetConfig().ServerURL,
		}

		// 嘗試獲取工具數量
		if connected {
			if tools, err := client.GetToolDefinitions(ctx); err == nil {
				clientStats["tool_count"] = len(tools)
			} else {
				clientStats["tool_count"] = 0
				clientStats["error"] = err.Error()
			}
		} else {
			clientStats["tool_count"] = 0
		}

		stats["clients"].(map[string]interface{})[clientName] = clientStats
	}

	stats["connected_clients"] = connectedCount
	return stats
}

// RefreshAllConnections 刷新所有客戶端連接
func (ui *UnifiedMCPIntegration) RefreshAllConnections(ctx context.Context) error {
	if ui.manager == nil {
		return fmt.Errorf("MCP not initialized")
	}

	connectionStatus := ui.manager.GetConnectionStatus()
	var errors []error

	for clientName := range connectionStatus {
		if err := ui.manager.ReconnectClient(ctx, clientName); err != nil {
			ui.logger.Errorf(ctx, "Failed to reconnect client '%s': %v", clientName, err)
			errors = append(errors, fmt.Errorf("client '%s': %w", clientName, err))
		} else {
			ui.logger.Debugf(ctx, "Successfully reconnected client '%s'", clientName)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("failed to reconnect some clients: %v", errors)
	}

	ui.logger.Infof(ctx, "All MCP clients reconnected successfully")
	return nil
}
