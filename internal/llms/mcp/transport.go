package mcp

import (
	"context"
	"fmt"
	"os/exec"

	"github.com/modelcontextprotocol/go-sdk/mcp"
)

// TransportType 傳輸類型常量
const (
	TransportTypeStdio      = "stdio"
	TransportTypeSSE        = "sse"
	TransportTypeStreamable = "streamable"
	TransportTypeHTTP       = "http" // 已棄用，將顯示警告
)

// MCPServerConfig MCP 服務器配置
type MCPServerConfig struct {
	Name        string            `json:"name" yaml:"name"`
	Type        string            `json:"type" yaml:"type"`
	Command     string            `json:"command,omitempty" yaml:"command,omitempty"`
	Args        []string          `json:"args,omitempty" yaml:"args,omitempty"`
	URL         string            `json:"url,omitempty" yaml:"url,omitempty"`
	Headers     map[string]string `json:"headers,omitempty" yaml:"headers,omitempty"`
	Environment map[string]string `json:"environment,omitempty" yaml:"environment,omitempty"`
	Timeout     int               `json:"timeout,omitempty" yaml:"timeout,omitempty"`
}

// TransportAdapter 傳輸適配器
type TransportAdapter struct {
	config MCPServerConfig
	logger Logger
}

// Logger 日誌接口
type Logger interface {
	Warningf(ctx context.Context, format string, args ...interface{})
	Errorf(ctx context.Context, format string, args ...interface{})
	Debugf(ctx context.Context, format string, args ...interface{})
}

// NewTransportAdapter 創建傳輸適配器
func NewTransportAdapter(config MCPServerConfig, logger Logger) *TransportAdapter {
	return &TransportAdapter{
		config: config,
		logger: logger,
	}
}

// CreateTransport 根據配置創建對應的傳輸
func (ta *TransportAdapter) CreateTransport(ctx context.Context) (mcp.Transport, error) {
	switch ta.config.Type {
	case TransportTypeStdio:
		return ta.createStdioTransport(ctx)
	case TransportTypeSSE:
		return ta.createSSETransport(ctx)
	case TransportTypeStreamable:
		return ta.createStreamableTransport(ctx)
	case TransportTypeHTTP:
		// HTTP協議不被官方SDK支持，提供遷移建議
		ta.logger.Warningf(ctx, "HTTP transport is deprecated and not supported by official SDK. Server '%s' will be skipped. Please migrate to 'streamable' transport.", ta.config.Name)
		return nil, fmt.Errorf("HTTP transport not supported, please migrate to streamable transport")
	default:
		return nil, fmt.Errorf("unsupported transport type: %s", ta.config.Type)
	}
}

// createStdioTransport 創建STDIO傳輸
func (ta *TransportAdapter) createStdioTransport(ctx context.Context) (mcp.Transport, error) {
	if ta.config.Command == "" {
		return nil, fmt.Errorf("command is required for stdio transport")
	}

	cmd := exec.Command(ta.config.Command, ta.config.Args...)

	// 設置環境變量
	if len(ta.config.Environment) > 0 {
		env := cmd.Environ()
		for key, value := range ta.config.Environment {
			env = append(env, fmt.Sprintf("%s=%s", key, value))
		}
		cmd.Env = env
	}

	ta.logger.Debugf(ctx, "Creating STDIO transport for server '%s' with command: %s %v",
		ta.config.Name, ta.config.Command, ta.config.Args)

	return &mcp.CommandTransport{
		Command: cmd,
	}, nil
}

// createSSETransport 創建SSE傳輸
func (ta *TransportAdapter) createSSETransport(ctx context.Context) (mcp.Transport, error) {
	if ta.config.URL == "" {
		return nil, fmt.Errorf("URL is required for SSE transport")
	}

	ta.logger.Debugf(ctx, "Creating SSE transport for server '%s' with URL: %s",
		ta.config.Name, ta.config.URL)

	return &mcp.SSEClientTransport{
		Endpoint: ta.config.URL,
	}, nil
}

// createStreamableTransport 創建Streamable傳輸
func (ta *TransportAdapter) createStreamableTransport(ctx context.Context) (mcp.Transport, error) {
	if ta.config.URL == "" {
		return nil, fmt.Errorf("URL is required for streamable transport")
	}

	ta.logger.Debugf(ctx, "Creating Streamable transport for server '%s' with URL: %s",
		ta.config.Name, ta.config.URL)

	return &mcp.StreamableClientTransport{
		Endpoint: ta.config.URL,
	}, nil
}

// ValidateConfig 驗證配置
func (ta *TransportAdapter) ValidateConfig() error {
	if ta.config.Name == "" {
		return fmt.Errorf("server name is required")
	}

	switch ta.config.Type {
	case TransportTypeStdio:
		if ta.config.Command == "" {
			return fmt.Errorf("command is required for stdio transport")
		}
	case TransportTypeSSE, TransportTypeStreamable:
		if ta.config.URL == "" {
			return fmt.Errorf("URL is required for %s transport", ta.config.Type)
		}
	case TransportTypeHTTP:
		// HTTP協議已棄用，但不阻止驗證
		return fmt.Errorf("HTTP transport is deprecated, please migrate to streamable transport")
	default:
		return fmt.Errorf("unsupported transport type: %s", ta.config.Type)
	}

	return nil
}
