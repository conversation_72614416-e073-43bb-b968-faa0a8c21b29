package mcp

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestConfigManager_LoadMCPConfig(t *testing.T) {
	ctx := context.Background()
	
	t.Run("config manager creation", func(t *testing.T) {
		cm := NewConfigManager()
		assert.NotNil(t, cm)
		assert.NotNil(t, cm.logger)
	})

	t.Run("load config with cache", func(t *testing.T) {
		cm := NewConfigManager()
		
		// 第一次加載
		config1, err1 := cm.LoadMCPConfig(ctx)
		
		// 第二次加載（應該使用緩存）
		config2, err2 := cm.LoadMCPConfig(ctx)
		
		// 兩次結果應該一致
		assert.Equal(t, err1, err2)
		if config1 != nil && config2 != nil {
			assert.Equal(t, config1.Enabled, config2.Enabled)
			assert.Equal(t, len(config1.Servers), len(config2.Servers))
		}
	})
}

func TestConfigManager_ValidateConfig(t *testing.T) {
	tests := []struct {
		name        string
		config      *MCPConfig
		expectError bool
		errorMsg    string
	}{
		{
			name:        "nil config",
			config:      nil,
			expectError: false, // nil config is valid (MCP disabled)
		},
		{
			name: "disabled config",
			config: &MCPConfig{
				Enabled: false,
			},
			expectError: false,
		},
		{
			name: "enabled with no servers",
			config: &MCPConfig{
				Enabled: true,
				Servers: []MCPServerConfig{},
			},
			expectError: true,
			errorMsg:    "no servers configured",
		},
		{
			name: "valid config with servers",
			config: &MCPConfig{
				Enabled: true,
				Servers: []MCPServerConfig{
					{
						Name:    "test-server",
						Type:    TransportTypeStdio,
						Command: "test-command",
					},
				},
			},
			expectError: false,
		},
		{
			name: "config with invalid server",
			config: &MCPConfig{
				Enabled: true,
				Servers: []MCPServerConfig{
					{
						Name: "invalid-server",
						Type: "invalid-type",
					},
				},
			},
			expectError: true,
			errorMsg:    "invalid server configuration",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cm := NewConfigManager()
			err := cm.ValidateConfig(tt.config)
			
			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestConfigManager_CacheExpiration(t *testing.T) {
	ctx := context.Background()
	cm := NewConfigManager()
	
	// 設置較短的緩存時間用於測試
	cm.cacheExpiry = 100 * time.Millisecond
	
	// 第一次加載
	config1, err1 := cm.LoadMCPConfig(ctx)
	require.NoError(t, err1)
	
	// 等待緩存過期
	time.Sleep(150 * time.Millisecond)
	
	// 第二次加載（緩存已過期，應該重新加載）
	config2, err2 := cm.LoadMCPConfig(ctx)
	require.NoError(t, err2)
	
	// 驗證兩次加載都成功
	if config1 != nil && config2 != nil {
		assert.Equal(t, config1.Enabled, config2.Enabled)
	}
}

func TestMCPServerConfig_Validation(t *testing.T) {
	tests := []struct {
		name   string
		config MCPServerConfig
		valid  bool
	}{
		{
			name: "valid stdio config",
			config: MCPServerConfig{
				Name:    "test",
				Type:    TransportTypeStdio,
				Command: "echo",
				Args:    []string{"hello"},
			},
			valid: true,
		},
		{
			name: "valid sse config",
			config: MCPServerConfig{
				Name: "test",
				Type: TransportTypeSSE,
				URL:  "http://localhost:8080",
			},
			valid: true,
		},
		{
			name: "valid streamable config",
			config: MCPServerConfig{
				Name: "test",
				Type: TransportTypeStreamable,
				URL:  "http://localhost:8080",
			},
			valid: true,
		},
		{
			name: "invalid - no name",
			config: MCPServerConfig{
				Type:    TransportTypeStdio,
				Command: "echo",
			},
			valid: false,
		},
		{
			name: "invalid - no type",
			config: MCPServerConfig{
				Name:    "test",
				Command: "echo",
			},
			valid: false,
		},
		{
			name: "invalid - stdio without command",
			config: MCPServerConfig{
				Name: "test",
				Type: TransportTypeStdio,
			},
			valid: false,
		},
		{
			name: "invalid - sse without url",
			config: MCPServerConfig{
				Name: "test",
				Type: TransportTypeSSE,
			},
			valid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			logger := &mockLogger{}
			adapter := NewTransportAdapter(tt.config, logger)
			err := adapter.ValidateConfig()
			
			if tt.valid {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
			}
		})
	}
}

func TestConfigManager_EnvironmentVariableExpansion(t *testing.T) {
	// 這個測試驗證環境變量展開功能
	// 由於GoFrame的配置系統會自動處理環境變量展開，
	// 我們主要測試配置結構的正確性
	
	config := &MCPConfig{
		Enabled: true,
		Servers: []MCPServerConfig{
			{
				Name:    "test-server",
				Type:    TransportTypeStdio,
				Command: "test-command",
				Environment: map[string]string{
					"API_KEY": "${API_KEY}",
					"HOST":    "${HOST:localhost}",
				},
			},
		},
	}

	cm := NewConfigManager()
	err := cm.ValidateConfig(config)
	assert.NoError(t, err)
	
	// 驗證環境變量配置結構
	assert.Len(t, config.Servers, 1)
	assert.Equal(t, "test-server", config.Servers[0].Name)
	assert.NotEmpty(t, config.Servers[0].Environment)
	assert.Contains(t, config.Servers[0].Environment, "API_KEY")
	assert.Contains(t, config.Servers[0].Environment, "HOST")
}
