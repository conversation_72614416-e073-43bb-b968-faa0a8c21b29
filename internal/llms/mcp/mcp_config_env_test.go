package mcp

import (
	"context"
	"os"
	"testing"
)

// 驗證：環境變數展開 + Global 設定合併
func TestConfig_EnvExpansionAndGlobalApply(t *testing.T) {
	ctx := context.Background()
	cm := NewConfigManager()

	// 設定環境變數
	os.Setenv("CMD", "/bin/echo")
	os.Setenv("TIMEOUT", "42")
	os.Setenv("HOME", "/home/<USER>")
	t.Cleanup(func() {
		os.Unsetenv("CMD")
		os.Unsetenv("TIMEOUT")
		os.Unsetenv("HOME")
	})

	cfg := &MCPConfig{
		Enabled: true,
		Global: MCPGlobalConfig{
			Timeout:     15,
			Environment: map[string]string{"G_TOKEN": "global-token"},
		},
		Servers: []MCPServerConfig{
			{
				Name:    "stdio-echo",
				Type:    TransportTypeStdio,
				Command: "${CMD}",
				Args:    []string{"-t", "${TIMEOUT}"},
				Environment: map[string]string{
					"LOCAL": "${HOME}",
				},
			},
		},
	}

	// 私有方法：在同 package 測試中可直接呼叫
	if err := cm.validateAndProcessConfig(ctx, cfg); err != nil {
		// 不要求實際啟動 stdio 程式，僅檢查展開與合併邏輯
		t.Fatalf("validateAndProcessConfig failed: %v", err)
	}

	if got, want := cfg.Servers[0].Command, "/bin/echo"; got != want {
		t.Fatalf("command not expanded: got=%q want=%q", got, want)
	}
	if got, want := cfg.Servers[0].Args[1], "42"; got != want {
		t.Fatalf("arg not expanded: got=%q want=%q", got, want)
	}
	if got, want := cfg.Servers[0].Environment["LOCAL"], "/home/<USER>"; got != want {
		t.Fatalf("env value not expanded: got=%q want=%q", got, want)
	}
	if got, want := cfg.Servers[0].Environment["G_TOKEN"], "global-token"; got != want {
		t.Fatalf("global env not merged: got=%q want=%q", got, want)
	}
	if got, want := cfg.Servers[0].Timeout, 15; got != want {
		t.Fatalf("global timeout not applied: got=%d want=%d", got, want)
	}
}

// 驗證：HTTP 已棄用配置會被擋下並提示遷移
func TestConfig_DeprecatedHTTPValidation(t *testing.T) {
	ctx := context.Background()
	cm := NewConfigManager()

	cfg := &MCPConfig{
		Enabled: true,
		Servers: []MCPServerConfig{
			{Name: "bad-http", Type: TransportTypeHTTP, URL: "http://localhost:8080"},
		},
	}

	err := cm.validateAndProcessConfig(ctx, cfg)
	if err == nil {
		t.Fatalf("expected error for deprecated HTTP transport, got nil")
	}
}

