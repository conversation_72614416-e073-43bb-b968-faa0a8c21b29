package mcp

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"brainHub/internal/llms/common"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

// UnifiedMCPManager 統一的 MCP 管理器，替代複雜的多層架構
type UnifiedMCPManager struct {
	clients map[string]*UnifiedMCPClient
	config  *UnifiedMCPConfig
	logger  glog.ILogger
	mutex   sync.RWMutex
}

// UnifiedMCPConfig 統一的 MCP 配置
type UnifiedMCPConfig struct {
	Enabled      bool               `json:"enabled" yaml:"enabled"`
	Clients      []*MCPClientConfig `json:"clients" yaml:"clients"`
	GlobalConfig *MCPGlobalConfig   `json:"global" yaml:"global"`
}

// NewUnifiedMCPManager 創建新的統一 MCP 管理器
func NewUnifiedMCPManager(config *UnifiedMCPConfig) *UnifiedMCPManager {
	logger := g.Log().Cat("unified-mcp-manager")

	// 設置默認值
	if config.GlobalConfig == nil {
		config.GlobalConfig = &MCPGlobalConfig{
			Timeout:     60, // 秒
			Environment: make(map[string]string),
		}
	}

	return &UnifiedMCPManager{
		clients: make(map[string]*UnifiedMCPClient),
		config:  config,
		logger:  logger,
	}
}

// Initialize 初始化所有 MCP 客戶端
func (m *UnifiedMCPManager) Initialize(ctx context.Context) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.config.Enabled {
		m.logger.Debugf(ctx, "MCP is disabled, skipping initialization")
		return nil
	}

	m.logger.Infof(ctx, "Initializing unified MCP manager with %d clients", len(m.config.Clients))

	var initErrors []error
	successCount := 0

	for _, clientConfig := range m.config.Clients {
		if clientConfig.Disabled {
			m.logger.Debugf(ctx, "Client '%s' is disabled, skipping", clientConfig.Name)
			continue
		}

		// 應用全局配置
		m.applyGlobalConfig(clientConfig)

		// 創建並初始化客戶端
		client := NewUnifiedMCPClient(clientConfig)
		if err := client.Initialize(ctx); err != nil {
			m.logger.Errorf(ctx, "Failed to initialize client '%s': %v", clientConfig.Name, err)
			initErrors = append(initErrors, fmt.Errorf("client '%s': %w", clientConfig.Name, err))
			continue
		}

		m.clients[clientConfig.Name] = client
		successCount++
		m.logger.Debugf(ctx, "Successfully initialized client '%s'", clientConfig.Name)
	}

	m.logger.Infof(ctx, "Unified MCP manager initialization completed: %d/%d clients successful",
		successCount, len(m.config.Clients))

	// 如果有部分失敗，記錄警告但不阻止初始化
	if len(initErrors) > 0 {
		m.logger.Warningf(ctx, "Some MCP clients failed to initialize: %v", initErrors)
	}

	return nil
}

// applyGlobalConfig 應用全局配置到客戶端配置
func (m *UnifiedMCPManager) applyGlobalConfig(clientConfig *MCPClientConfig) {
	if clientConfig.Timeout == 0 && m.config.GlobalConfig.Timeout > 0 {
		clientConfig.Timeout = time.Duration(m.config.GlobalConfig.Timeout) * time.Second
	}
	if clientConfig.MaxRetries == 0 {
		clientConfig.MaxRetries = 3 // 默認重試次數
	}
}

// GetToolDefinitions 獲取所有工具定義
func (m *UnifiedMCPManager) GetToolDefinitions(ctx context.Context) ([]common.ToolDefinition, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	m.logger.Debugf(ctx, "Getting tool definitions from all MCP clients")

	var allTools []common.ToolDefinition
	for clientName, client := range m.clients {
		if !client.IsConnected() {
			m.logger.Warningf(ctx, "Client '%s' is not connected, skipping tool definitions", clientName)
			continue
		}

		tools, err := client.GetToolDefinitions(ctx)
		if err != nil {
			m.logger.Errorf(ctx, "Failed to get tool definitions from client '%s': %v", clientName, err)
			continue
		}

		allTools = append(allTools, tools...)
		m.logger.Debugf(ctx, "Retrieved %d tools from client '%s'", len(tools), clientName)
	}

	m.logger.Debugf(ctx, "Total tool definitions retrieved: %d", len(allTools))
	return allTools, nil
}

// CallTool 調用工具
func (m *UnifiedMCPManager) CallTool(ctx context.Context, toolName string, args map[string]interface{}) (*common.ToolResult, error) {
	m.logger.Debugf(ctx, "Calling tool: %s with args: %v", toolName, args)

	// 解析工具名稱（格式：clientName.toolName）
	clientName, actualToolName, err := m.parseToolName(toolName)
	if err != nil {
		return &common.ToolResult{
			Success: false,
			Error:   fmt.Sprintf("Invalid tool name format: %v", err),
		}, nil
	}

	// 獲取客戶端
	m.mutex.RLock()
	client, exists := m.clients[clientName]
	m.mutex.RUnlock()

	if !exists {
		return &common.ToolResult{
			Success: false,
			Error:   fmt.Sprintf("Client '%s' not found", clientName),
		}, nil
	}

	if !client.IsConnected() {
		return &common.ToolResult{
			Success: false,
			Error:   fmt.Sprintf("Client '%s' is not connected", clientName),
		}, nil
	}

	// 調用工具
	return client.CallTool(ctx, actualToolName, args)
}

// parseToolName 解析工具名稱
func (m *UnifiedMCPManager) parseToolName(toolName string) (clientName, actualToolName string, err error) {
	parts := strings.SplitN(toolName, ".", 2)
	if len(parts) != 2 {
		return "", "", fmt.Errorf("tool name must be in format 'clientName.toolName', got: %s", toolName)
	}
	return parts[0], parts[1], nil
}

// GetConnectionStatus 獲取所有客戶端的連接狀態
func (m *UnifiedMCPManager) GetConnectionStatus() map[string]bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	status := make(map[string]bool)
	for name, client := range m.clients {
		status[name] = client.IsConnected()
	}
	return status
}

// ReconnectClient 重新連接指定的客戶端
func (m *UnifiedMCPManager) ReconnectClient(ctx context.Context, clientName string) error {
	m.mutex.RLock()
	client, exists := m.clients[clientName]
	m.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("client '%s' not found", clientName)
	}

	m.logger.Infof(ctx, "Reconnecting client '%s'...", clientName)
	return client.Reconnect(ctx)
}

// Close 關閉所有客戶端連接
func (m *UnifiedMCPManager) Close(ctx context.Context) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	var closeErrors []error

	for name, client := range m.clients {
		if err := client.Close(); err != nil {
			m.logger.Errorf(ctx, "Failed to close client '%s': %v", name, err)
			closeErrors = append(closeErrors, fmt.Errorf("client '%s': %w", name, err))
		} else {
			m.logger.Debugf(ctx, "Successfully closed client '%s'", name)
		}
	}

	if len(closeErrors) > 0 {
		return fmt.Errorf("errors closing clients: %v", closeErrors)
	}

	m.logger.Debugf(ctx, "All MCP clients closed successfully")
	return nil
}

// IsEnabled 檢查 MCP 是否啟用
func (m *UnifiedMCPManager) IsEnabled() bool {
	return m.config.Enabled
}

// GetClientCount 獲取客戶端數量
func (m *UnifiedMCPManager) GetClientCount() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return len(m.clients)
}

// GetClient 獲取指定名稱的客戶端
func (m *UnifiedMCPManager) GetClient(name string) (*UnifiedMCPClient, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	client, exists := m.clients[name]
	return client, exists
}

// LoadConfigFromGoFrame 從 GoFrame 配置載入 MCP 配置
func LoadUnifiedMCPConfig(ctx context.Context) (*UnifiedMCPConfig, error) {
	logger := g.Log().Cat("mcp-config-loader")

	// 嘗試從不同的配置路徑載入
	configPaths := []string{
		"mcp_config",
		"mcp_servers",
		"mcp",
	}

	for _, path := range configPaths {
		if config, err := g.Cfg().Get(ctx, path); err == nil && config != nil {
			logger.Debugf(ctx, "Found MCP config at path: %s", path)

			// 解析配置
			var mcpConfig struct {
				Enabled bool              `yaml:"enabled"`
				Servers []MCPServerConfig `yaml:"servers"`
				Global  MCPGlobalConfig   `yaml:"global"`
			}

			if err := config.Scan(&mcpConfig); err != nil {
				logger.Warningf(ctx, "Failed to parse MCP config from path '%s': %v", path, err)
				continue
			}

			// 轉換為統一配置格式
			unifiedConfig := &UnifiedMCPConfig{
				Enabled:      mcpConfig.Enabled,
				GlobalConfig: &mcpConfig.Global,
			}

			// 轉換服務器配置為客戶端配置
			for _, serverConfig := range mcpConfig.Servers {
				clientConfig := &MCPClientConfig{
					Name:        serverConfig.Name,
					ServerURL:   serverConfig.URL,
					Type:        serverConfig.Type,
					Headers:     make(map[string]string),
					Environment: serverConfig.Environment,
					Timeout:     time.Duration(serverConfig.Timeout) * time.Second,
				}

				// 設置默認超時時間
				if clientConfig.Timeout == 0 {
					clientConfig.Timeout = 60 * time.Second
				}

				unifiedConfig.Clients = append(unifiedConfig.Clients, clientConfig)
			}

			logger.Debugf(ctx, "Loaded unified MCP config: enabled=%v, clients=%d",
				unifiedConfig.Enabled, len(unifiedConfig.Clients))

			return unifiedConfig, nil
		}
	}

	logger.Debugf(ctx, "No MCP configuration found")
	return &UnifiedMCPConfig{Enabled: false}, nil
}
