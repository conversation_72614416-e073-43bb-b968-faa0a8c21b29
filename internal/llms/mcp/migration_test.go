package mcp

import (
	"context"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestConfigMigrationTool_AnalyzeSingleServer(t *testing.T) {
	cmt := NewConfigMigrationTool()

	tests := []struct {
		name             string
		server           MCPServerConfig
		expectSuggestion bool
		expectedType     string
	}{
		{
			name: "http server needs migration",
			server: MCPServerConfig{
				Name: "test-http",
				Type: TransportTypeHTTP,
				URL:  "http://localhost:8080",
			},
			expectSuggestion: true,
			expectedType:     TransportTypeStreamable,
		},
		{
			name: "stdio server no migration needed",
			server: MCPServerConfig{
				Name:    "test-stdio",
				Type:    TransportTypeStdio,
				Command: "test-command",
			},
			expectSuggestion: false,
		},
		{
			name: "sse server no migration needed",
			server: MCPServerConfig{
				Name: "test-sse",
				Type: TransportTypeSSE,
				URL:  "http://localhost:8080",
			},
			expectSuggestion: false,
		},
		{
			name: "streamable server no migration needed",
			server: MCPServerConfig{
				Name: "test-streamable",
				Type: TransportTypeStreamable,
				URL:  "http://localhost:8080",
			},
			expectSuggestion: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			suggestion := cmt.analyzeSingleServer(tt.server)

			if tt.expectSuggestion {
				require.NotNil(t, suggestion)
				assert.Equal(t, tt.server.Name, suggestion.ServerName)
				assert.Equal(t, tt.server.Type, suggestion.CurrentType)
				assert.Equal(t, tt.expectedType, suggestion.SuggestedType)
				assert.NotEmpty(t, suggestion.Reason)
				assert.NotEmpty(t, suggestion.ConfigExample)
			} else {
				assert.Nil(t, suggestion)
			}
		})
	}
}

func TestConfigMigrationTool_GenerateStreamableConfigExample(t *testing.T) {
	cmt := NewConfigMigrationTool()

	server := MCPServerConfig{
		Name:    "test-server",
		Type:    TransportTypeHTTP,
		URL:     "http://localhost:8080",
		Timeout: 30,
		Environment: map[string]string{
			"API_KEY": "test-key",
			"DEBUG":   "true",
		},
	}

	example := cmt.generateStreamableConfigExample(server)

	assert.Contains(t, example, "test-server")
	assert.Contains(t, example, "type: \"streamable\"")
	assert.Contains(t, example, "url: \"http://localhost:8080\"")
	assert.Contains(t, example, "timeout: 30")
	assert.Contains(t, example, "API_KEY: \"test-key\"")
	assert.Contains(t, example, "DEBUG: \"true\"")
	assert.Contains(t, example, "# Migrated configuration")
}

func TestConfigMigrationTool_GenerateMigrationReport(t *testing.T) {
	ctx := context.Background()
	cmt := NewConfigMigrationTool()

	t.Run("no migration needed", func(t *testing.T) {
		// 這個測試假設當前沒有HTTP配置需要遷移
		// 實際測試中可能需要模擬配置
		report, err := cmt.GenerateMigrationReport(ctx)
		require.NoError(t, err)

		// 如果沒有需要遷移的配置，應該返回成功消息
		if strings.Contains(report, "No MCP configuration migration needed") {
			assert.Contains(t, report, "✅")
		} else {
			// 如果有需要遷移的配置，應該包含遷移信息
			assert.Contains(t, report, "# MCP Configuration Migration Report")
		}
	})
}

func TestConfigMigrationTool_GetDeprecationWarnings(t *testing.T) {
	ctx := context.Background()
	cmt := NewConfigMigrationTool()

	warnings := cmt.GetDeprecationWarnings(ctx)

	// 警告可能為空（如果沒有HTTP配置）或包含HTTP棄用警告
	for _, warning := range warnings {
		assert.Contains(t, warning, "⚠️")
		assert.Contains(t, warning, "deprecated HTTP transport")
	}
}

func TestConfigMigrationTool_ValidatePostMigration(t *testing.T) {
	ctx := context.Background()
	cmt := NewConfigMigrationTool()

	// 測試遷移後驗證
	err := cmt.ValidatePostMigration(ctx)

	// 如果有HTTP配置存在，應該返回錯誤
	// 如果沒有HTTP配置，應該通過驗證
	if err != nil {
		assert.Contains(t, err.Error(), "deprecated HTTP transport")
	}
}

func TestMigrationSuggestion_Structure(t *testing.T) {
	suggestion := MigrationSuggestion{
		ServerName:    "test-server",
		CurrentType:   TransportTypeHTTP,
		SuggestedType: TransportTypeStreamable,
		Reason:        "HTTP transport is deprecated",
		ConfigExample: "example config",
	}

	assert.Equal(t, "test-server", suggestion.ServerName)
	assert.Equal(t, TransportTypeHTTP, suggestion.CurrentType)
	assert.Equal(t, TransportTypeStreamable, suggestion.SuggestedType)
	assert.NotEmpty(t, suggestion.Reason)
	assert.NotEmpty(t, suggestion.ConfigExample)
}

func TestConfigMigrationTool_Integration(t *testing.T) {
	ctx := context.Background()
	cmt := NewConfigMigrationTool()

	// 測試完整的遷移工作流程
	t.Run("full migration workflow", func(t *testing.T) {
		// 1. 分析配置
		suggestions, err := cmt.AnalyzeConfiguration(ctx)
		require.NoError(t, err)

		// 2. 生成報告
		report, err := cmt.GenerateMigrationReport(ctx)
		require.NoError(t, err)
		assert.NotEmpty(t, report)

		// 3. 獲取警告
		warnings := cmt.GetDeprecationWarnings(ctx)
		// warnings 可能為空或包含警告

		// 4. 驗證遷移後狀態
		err = cmt.ValidatePostMigration(ctx)
		// err 可能為nil或包含驗證錯誤

		// 驗證數據一致性
		if len(suggestions) > 0 {
			// 如果有遷移建議，報告應該包含遷移信息
			assert.Contains(t, report, "Migration Report")
		}

		if len(warnings) > 0 {
			// 如果有警告，應該都是關於HTTP的
			for _, warning := range warnings {
				assert.Contains(t, warning, "HTTP transport")
			}
		}
	})
}

func TestConfigMigrationTool_EdgeCases(t *testing.T) {
	cmt := NewConfigMigrationTool()

	t.Run("empty server name", func(t *testing.T) {
		server := MCPServerConfig{
			Name: "",
			Type: TransportTypeHTTP,
			URL:  "http://localhost:8080",
		}

		suggestion := cmt.analyzeSingleServer(server)
		require.NotNil(t, suggestion)
		assert.Equal(t, "", suggestion.ServerName)
	})

	t.Run("missing url in http config", func(t *testing.T) {
		server := MCPServerConfig{
			Name: "test-server",
			Type: TransportTypeHTTP,
			URL:  "",
		}

		suggestion := cmt.analyzeSingleServer(server)
		require.NotNil(t, suggestion)
		assert.Equal(t, TransportTypeStreamable, suggestion.SuggestedType)
	})

	t.Run("complex environment variables", func(t *testing.T) {
		server := MCPServerConfig{
			Name: "test-server",
			Type: TransportTypeHTTP,
			URL:  "http://localhost:8080",
			Environment: map[string]string{
				"COMPLEX_VAR": "${VAR1}:${VAR2:-default}",
				"EMPTY_VAR":   "",
			},
		}

		example := cmt.generateStreamableConfigExample(server)
		assert.Contains(t, example, "COMPLEX_VAR")
		assert.Contains(t, example, "EMPTY_VAR")
	})
}
