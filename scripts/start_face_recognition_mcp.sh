#!/bin/bash

# 人臉識別 MCP 服務器啟動腳本

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 檢查環境變數
check_env() {
    log_info "檢查環境變數..."
    
    if [ -z "$FACE_RECOGNITION_API_KEY" ]; then
        log_error "FACE_RECOGNITION_API_KEY 環境變數未設置"
        log_info "請設置環境變數："
        echo "export FACE_RECOGNITION_API_KEY=\"NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU\""
        exit 1
    fi
    
    log_info "✓ FACE_RECOGNITION_API_KEY 已設置"
}

# 構建應用程序
build_app() {
    log_info "構建人臉識別 MCP 服務器..."
    
    cd "$(dirname "$0")/.."
    
    if ! go build -o bin/face-recognition-mcp ./cmd/face_recognition_mcp; then
        log_error "構建失敗"
        exit 1
    fi
    
    log_info "✓ 構建完成"
}

# 啟動服務器
start_server() {
    log_info "啟動人臉識別 MCP 服務器..."
    
    # 設置默認值
    export FACE_RECOGNITION_BASE_URL="${FACE_RECOGNITION_BASE_URL:-https://csai_uat_deepface.chainsea.com.tw}"
    export MCP_SERVER_PORT="${MCP_SERVER_PORT:-8080}"
    
    log_info "配置信息："
    log_info "  Base URL: $FACE_RECOGNITION_BASE_URL"
    log_info "  Port: $MCP_SERVER_PORT"
    log_info "  API Key: ${FACE_RECOGNITION_API_KEY:0:10}..."
    
    # 啟動服務器
    ./bin/face-recognition-mcp \
        -base-url="$FACE_RECOGNITION_BASE_URL" \
        -api-key="$FACE_RECOGNITION_API_KEY" \
        -port="$MCP_SERVER_PORT" \
        -timeout=60
}

# 使用 Docker 啟動
start_with_docker() {
    log_info "使用 Docker 啟動人臉識別 MCP 服務器..."
    
    cd "$(dirname "$0")/../cmd/face_recognition_mcp"
    
    # 檢查 Docker 是否可用
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安裝或不可用"
        exit 1
    fi
    
    # 使用 docker-compose 啟動
    if command -v docker-compose &> /dev/null; then
        log_info "使用 docker-compose 啟動..."
        FACE_RECOGNITION_API_KEY="$FACE_RECOGNITION_API_KEY" docker-compose up --build
    else
        log_info "使用 docker 啟動..."
        docker build -t face-recognition-mcp -f Dockerfile ../../
        docker run -p 8080:8080 \
            -e FACE_RECOGNITION_BASE_URL="https://csai_uat_deepface.chainsea.com.tw" \
            -e FACE_RECOGNITION_API_KEY="$FACE_RECOGNITION_API_KEY" \
            -e MCP_SERVER_PORT="8080" \
            face-recognition-mcp
    fi
}

# 測試服務器
test_server() {
    log_info "測試 MCP 服務器..."
    
    local port="${MCP_SERVER_PORT:-8080}"
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "http://localhost:$port/" > /dev/null; then
            log_info "✓ 服務器正在運行"
            break
        fi
        
        log_debug "等待服務器啟動... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "服務器啟動超時"
        exit 1
    fi
    
    # 測試 MCP 協議
    log_info "測試 MCP 協議..."
    
    local response=$(curl -s -X POST "http://localhost:$port/" \
        -H "Content-Type: application/json" \
        -d '{"jsonrpc":"2.0","method":"tools/list","id":1}')
    
    if echo "$response" | grep -q "tools"; then
        log_info "✓ MCP 協議測試通過"
        log_debug "響應: $response"
    else
        log_error "MCP 協議測試失敗"
        log_debug "響應: $response"
        exit 1
    fi
}

# 顯示幫助信息
show_help() {
    echo "人臉識別 MCP 服務器啟動腳本"
    echo ""
    echo "用法: $0 [選項]"
    echo ""
    echo "選項:"
    echo "  -h, --help     顯示此幫助信息"
    echo "  -d, --docker   使用 Docker 啟動"
    echo "  -t, --test     僅測試服務器（不啟動）"
    echo "  -b, --build    僅構建應用程序"
    echo ""
    echo "環境變數:"
    echo "  FACE_RECOGNITION_API_KEY    人臉識別 API 金鑰（必需）"
    echo "  FACE_RECOGNITION_BASE_URL   人臉識別 API 基礎 URL"
    echo "  MCP_SERVER_PORT             MCP 服務器端口（默認: 8080）"
    echo ""
    echo "示例:"
    echo "  export FACE_RECOGNITION_API_KEY=\"your-api-key\""
    echo "  $0                          # 構建並啟動服務器"
    echo "  $0 --docker                 # 使用 Docker 啟動"
    echo "  $0 --test                   # 測試現有服務器"
}

# 主函數
main() {
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--docker)
            check_env
            start_with_docker
            ;;
        -t|--test)
            test_server
            ;;
        -b|--build)
            build_app
            ;;
        "")
            check_env
            build_app
            start_server
            ;;
        *)
            log_error "未知選項: $1"
            show_help
            exit 1
            ;;
    esac
}

# 執行主函數
main "$@"
