package main

import (
	"context"
	"flag"
	"log"
	"os"
	"os/signal"
	"syscall"

	"brainHub/internal/llms/mcp/servers"
)

func main() {
	var (
		baseURL = flag.String("base-url", "", "Face recognition API base URL")
		apiKey  = flag.String("api-key", "", "Face recognition API key")
		port    = flag.String("port", "8080", "MCP server port")
		timeout = flag.Int("timeout", 60, "Request timeout in seconds")
	)
	flag.Parse()

	// 從環境變數獲取配置（如果命令行參數未提供）
	if *baseURL == "" {
		*baseURL = os.Getenv("FACE_RECOGNITION_BASE_URL")
		if *baseURL == "" {
			*baseURL = "https://csai_uat_deepface.chainsea.com.tw"
		}
	}

	if *apiKey == "" {
		*apiKey = os.Getenv("FACE_RECOGNITION_API_KEY")
		if *apiKey == "" {
			log.Fatal("Face recognition API key is required. Set FACE_RECOGNITION_API_KEY environment variable or use -api-key flag")
		}
	}

	if envPort := os.Getenv("MCP_SERVER_PORT"); envPort != "" {
		*port = envPort
	}

	// 創建配置
	config := &servers.FaceRecognitionConfig{
		BaseURL: *baseURL,
		APIKey:  *apiKey,
		Timeout: *timeout,
	}

	// 創建 MCP 服務器
	server := servers.NewFaceRecognitionMCPServer(config, *port)

	// 創建上下文和信號處理
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 監聽系統信號
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		log.Println("Received shutdown signal, stopping server...")
		cancel()
	}()

	// 啟動服務器
	log.Printf("Starting Face Recognition MCP Server on port %s", *port)
	log.Printf("Base URL: %s", *baseURL)
	log.Printf("Timeout: %d seconds", *timeout)

	if err := server.Start(ctx); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}

	log.Println("Face Recognition MCP Server stopped")
}
