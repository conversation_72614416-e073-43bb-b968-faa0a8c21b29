# 使用官方 Go 鏡像作為構建階段
FROM golang:1.21-alpine AS builder

# 設置工作目錄
WORKDIR /app

# 複製 go mod 文件
COPY go.mod go.sum ./

# 下載依賴
RUN go mod download

# 複製源代碼
COPY . .

# 構建應用程序
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o face-recognition-mcp ./cmd/face_recognition_mcp

# 使用輕量級的 alpine 鏡像作為運行階段
FROM alpine:latest

# 安裝 ca-certificates 以支持 HTTPS 請求
RUN apk --no-cache add ca-certificates

# 創建非 root 用戶
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

WORKDIR /root/

# 從構建階段複製二進制文件
COPY --from=builder /app/face-recognition-mcp .

# 更改所有權
RUN chown appuser:appgroup face-recognition-mcp

# 切換到非 root 用戶
USER appuser

# 暴露端口
EXPOSE 8080

# 設置健康檢查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8080/ || exit 1

# 運行應用程序
CMD ["./face-recognition-mcp"]
