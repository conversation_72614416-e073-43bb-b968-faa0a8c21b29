# 人臉識別 MCP 包裝器服務器

這是一個 MCP (Model Context Protocol) 包裝器服務器，將人臉識別 API 包裝成標準的 MCP 工具，使 AI 模型能夠直接調用人臉識別功能。

## 功能特性

### 🎯 **核心功能**
- **人臉提取**: 從圖像中提取人臉特徵
- **人臉搜索**: 在聯絡人數據庫中搜索相似人臉
- **聯絡人管理**: 新增、查詢、更新聯絡人資訊
- **人臉綁定**: 將人臉圖像綁定到特定聯絡人
- **名人搜索**: 搜尋名人照片

### 🛠️ **可用工具**
1. `extract_faces_from_image_base64` - 從 Base64 圖像提取人臉
2. `extract_faces_from_image_url` - 從圖像 URL 提取人臉
3. `search_face_from_image_base64` - 使用 Base64 圖像搜索人臉
4. `search_face_from_image_url` - 使用圖像 URL 搜索人臉
5. `add_contact` - 新增聯絡人
6. `fetch_contacts` - 獲取聯絡人列表
7. `bind_contact_face` - 綁定人臉到聯絡人
8. `search_celebrity_photos` - 搜尋名人照片

## 快速開始

### 📋 **前置需求**
- Go 1.21 或更高版本
- 人臉識別 API 金鑰
- Docker (可選，用於容器化部署)

### 🚀 **方法一：使用啟動腳本（推薦）**

1. **設置環境變數**：
```bash
export FACE_RECOGNITION_API_KEY="NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU"
export FACE_RECOGNITION_BASE_URL="https://csai_uat_deepface.chainsea.com.tw"  # 可選
export MCP_SERVER_PORT="8080"  # 可選
```

2. **啟動服務器**：
```bash
# 構建並啟動
./scripts/start_face_recognition_mcp.sh

# 使用 Docker 啟動
./scripts/start_face_recognition_mcp.sh --docker

# 僅測試服務器
./scripts/start_face_recognition_mcp.sh --test
```

### 🐳 **方法二：使用 Docker**

1. **使用 docker-compose**：
```bash
cd cmd/face_recognition_mcp
export FACE_RECOGNITION_API_KEY="your-api-key"
docker-compose up --build
```

2. **使用 Docker 命令**：
```bash
# 構建鏡像
docker build -t face-recognition-mcp -f cmd/face_recognition_mcp/Dockerfile .

# 運行容器
docker run -p 8080:8080 \
  -e FACE_RECOGNITION_API_KEY="your-api-key" \
  -e FACE_RECOGNITION_BASE_URL="https://csai_uat_deepface.chainsea.com.tw" \
  face-recognition-mcp
```

### ⚙️ **方法三：手動構建**

```bash
# 構建
go build -o bin/face-recognition-mcp ./cmd/face_recognition_mcp

# 運行
./bin/face-recognition-mcp \
  -api-key="your-api-key" \
  -base-url="https://csai_uat_deepface.chainsea.com.tw" \
  -port="8080"
```

## 配置說明

### 🔧 **環境變數**
| 變數名 | 描述 | 默認值 | 必需 |
|--------|------|--------|------|
| `FACE_RECOGNITION_API_KEY` | 人臉識別 API 金鑰 | - | ✅ |
| `FACE_RECOGNITION_BASE_URL` | API 基礎 URL | `https://csai_uat_deepface.chainsea.com.tw` | ❌ |
| `MCP_SERVER_PORT` | MCP 服務器端口 | `8080` | ❌ |

### 🎛️ **命令行參數**
```bash
./face-recognition-mcp [選項]

選項:
  -api-key string     人臉識別 API 金鑰
  -base-url string    人臉識別 API 基礎 URL
  -port string        MCP 服務器端口 (默認 "8080")
  -timeout int        請求超時時間（秒） (默認 60)
```

## MCP 集成

### 📝 **配置 brainHub**

在 `brainHub` 的配置文件中添加以下配置：

```yaml
mcp_config:
  enabled: true
  default_timeout: "30s"

mcp_servers:
  - name: "faceRecognition"
    type: "streamable"
    description: "人臉識別 MCP 包裝器服務器"
    url: "http://localhost:8080/"
    timeout: 60
    environment:
      FACE_RECOGNITION_API_KEY: "${FACE_RECOGNITION_API_KEY}"
```

### 🧪 **測試 MCP 協議**

```bash
# 測試工具列表
curl -X POST http://localhost:8080/ \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"tools/list","id":1}'

# 測試工具調用
curl -X POST http://localhost:8080/ \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc":"2.0",
    "method":"tools/call",
    "params":{
      "name":"extract_faces_from_image_url",
      "arguments":{
        "image_url":"https://example.com/image.jpg"
      }
    },
    "id":2
  }'
```

## 使用示例

### 👤 **人臉提取**
```json
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "extract_faces_from_image_base64",
    "arguments": {
      "image_b64": "iVBORw0KGgoAAAANSUhEUgAA..."
    }
  },
  "id": 1
}
```

### 🔍 **人臉搜索**
```json
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "search_face_from_image_base64",
    "arguments": {
      "member_id": "user123",
      "image_b64": "iVBORw0KGgoAAAANSUhEUgAA...",
      "threshold": 0.8
    }
  },
  "id": 2
}
```

### 📇 **聯絡人管理**
```json
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "add_contact",
    "arguments": {
      "member_id": "user123",
      "name": "張三",
      "company": "ABC 公司",
      "phone": "0912345678",
      "email": "<EMAIL>"
    }
  },
  "id": 3
}
```

## 故障排除

### ❗ **常見問題**

1. **API 金鑰錯誤**
   - 確認 `FACE_RECOGNITION_API_KEY` 環境變數正確設置
   - 檢查 API 金鑰是否有效

2. **連接超時**
   - 檢查網絡連接
   - 確認人臉識別 API 服務器可訪問
   - 增加超時時間設置

3. **端口衝突**
   - 更改 `MCP_SERVER_PORT` 環境變數
   - 使用 `-port` 參數指定不同端口

### 📊 **健康檢查**
```bash
# 檢查服務器狀態
curl -I http://localhost:8080/

# 檢查 MCP 協議
curl -X POST http://localhost:8080/ \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"initialize","id":1}'
```

## 開發說明

### 🏗️ **項目結構**
```
cmd/face_recognition_mcp/
├── main.go              # 主程序入口
├── Dockerfile           # Docker 構建文件
├── docker-compose.yml   # Docker Compose 配置
└── README.md           # 說明文檔

internal/llms/mcp/servers/
├── face_recognition_wrapper.go      # API 包裝器
└── face_recognition_mcp_server.go   # MCP 服務器實現

scripts/
└── start_face_recognition_mcp.sh    # 啟動腳本
```

### 🔄 **擴展功能**
要添加新的人臉識別功能：

1. 在 `FaceRecognitionWrapper` 中添加新的工具定義
2. 實現對應的工具調用方法
3. 更新 `CallTool` 方法中的路由
4. 重新構建和部署

## 許可證

本項目遵循 MIT 許可證。
