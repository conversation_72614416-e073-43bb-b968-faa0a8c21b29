version: '3.8'

services:
  face-recognition-mcp:
    build:
      context: ../../
      dockerfile: cmd/face_recognition_mcp/Dockerfile
    ports:
      - "8080:8080"
    environment:
      - FACE_RECOGNITION_BASE_URL=https://csai_uat_deepface.chainsea.com.tw
      - FACE_RECOGNITION_API_KEY=${FACE_RECOGNITION_API_KEY}
      - MCP_SERVER_PORT=8080
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
